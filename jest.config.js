// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFiles: ['dotenv/config'],  // loads .env before tests
  testPathIgnorePatterns: ['/node_modules/', '/contracts/'],
  // Use .env.test file for tests
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  collectCoverage: true,
  collectCoverageFrom: [
    'backend/src/**/*.ts',
    '!**/node_modules/**',
    '!**/dist/**'
  ],
  coverageReporters: ['text', 'lcov', 'clover', 'html'],
  // Enable coverage thresholds
  coverageThreshold: {
    // Temporarily lower global thresholds
    global: {
      branches: 14,
      functions: 36,
      lines: 57,
      statements: 56
    },
    // Specific thresholds for payment services
    'backend/src/services/payments/aggregator.ts': {
      branches: 60,
      functions: 70,
      lines: 90,
      statements: 90
    },
    'backend/src/services/payments/common.ts': {
      branches: 50,
      functions: 70,
      lines: 90,
      statements: 90
    },
    'backend/src/services/payments/crypto.ts': {
      branches: 33,
      functions: 70,
      lines: 90,
      statements: 90
    },
    'backend/src/services/payments/mpesa.ts': {
      branches: 60,
      functions: 60,
      lines: 70,
      statements: 70
    },
    'backend/src/services/payments/pesapal.ts': {
      branches: 60,
      functions: 60,
      lines: 70,
      statements: 70
    },
    'backend/src/services/payments/index.ts': {
      branches: 0,
      functions: 0,
      lines: 50,
      statements: 50
    },
    // High threshold for jobs
    'backend/src/jobs/*.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
