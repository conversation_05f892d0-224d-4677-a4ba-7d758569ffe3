<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safaricom API Proxy Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea, button {
            padding: 8px;
            width: 100%;
        }
        textarea {
            height: 150px;
            font-family: monospace;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Safaricom API Proxy Test</h1>
    
    <div class="container">
        <h2>Test OAuth Token</h2>
        <p>This will test getting an OAuth token from Safaricom.</p>
        
        <button id="testOAuthBtn">Test OAuth Token</button>
        
        <div id="oauthLoading" class="loading">Processing...</div>
        <div id="oauthError" class="error"></div>
        <div id="oauthResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h2>Test STK Push</h2>
        
        <div class="form-group">
            <label for="phoneNumber">Phone Number (format: 254XXXXXXXXX)</label>
            <input type="text" id="phoneNumber" value="254708374149" placeholder="254XXXXXXXXX">
            <small>Use 254708374149 for successful test payments</small>
        </div>
        
        <div class="form-group">
            <label for="amount">Amount (KES)</label>
            <input type="number" id="amount" value="1" min="1" max="100">
            <small>Use a small amount for testing</small>
        </div>
        
        <button id="testStkBtn">Test STK Push</button>
        
        <div id="stkLoading" class="loading">Processing...</div>
        <div id="stkError" class="error"></div>
        <div id="stkResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h2>Custom API Call</h2>
        
        <div class="form-group">
            <label for="endpoint">Endpoint</label>
            <input type="text" id="endpoint" value="oauth/v1/generate" placeholder="e.g., oauth/v1/generate">
            <small>The endpoint path after https://sandbox.safaricom.co.ke/</small>
        </div>
        
        <div class="form-group">
            <label for="method">Method</label>
            <select id="method">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="auth">Authentication</label>
            <select id="auth">
                <option value="none">None</option>
                <option value="oauth">OAuth</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="data">Request Data (JSON)</label>
            <textarea id="data">{}</textarea>
        </div>
        
        <button id="customCallBtn">Make API Call</button>
        
        <div id="customLoading" class="loading">Processing...</div>
        <div id="customError" class="error"></div>
        <div id="customResult" class="result" style="display: none;"></div>
    </div>
    
    <script>
        // API URL
        const API_URL = 'http://localhost:3000';
        
        // Test OAuth Token
        document.getElementById('testOAuthBtn').addEventListener('click', async () => {
            const loadingEl = document.getElementById('oauthLoading');
            const errorEl = document.getElementById('oauthError');
            const resultEl = document.getElementById('oauthResult');
            
            try {
                loadingEl.style.display = 'block';
                errorEl.textContent = '';
                resultEl.style.display = 'none';
                
                const response = await fetch(`${API_URL}/api/payments/safaricom-proxy/oauth/v1/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        method: 'GET',
                        data: { grant_type: 'client_credentials' }
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Failed to get OAuth token');
                }
                
                resultEl.textContent = JSON.stringify(data, null, 2);
                resultEl.style.display = 'block';
            } catch (error) {
                errorEl.textContent = error.message;
            } finally {
                loadingEl.style.display = 'none';
            }
        });
        
        // Test STK Push
        document.getElementById('testStkBtn').addEventListener('click', async () => {
            const phoneNumber = document.getElementById('phoneNumber').value.trim();
            const amount = parseInt(document.getElementById('amount').value);
            const loadingEl = document.getElementById('stkLoading');
            const errorEl = document.getElementById('stkError');
            const resultEl = document.getElementById('stkResult');
            
            if (!phoneNumber || !amount) {
                errorEl.textContent = 'Please fill in all fields';
                return;
            }
            
            try {
                loadingEl.style.display = 'block';
                errorEl.textContent = '';
                resultEl.style.display = 'none';
                
                const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
                
                const response = await fetch(`${API_URL}/api/payments/safaricom-proxy/mpesa/stkpush/v1/processrequest`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        method: 'POST',
                        auth: 'oauth',
                        data: {
                            BusinessShortCode: '174379',
                            Password: 'MTc0Mzc5YmZiMjc5ZjlhYTliZGJjZjE1OGU5N2RkNzFhNDY3Y2QyZTBjODkzMDU5YjEwZjc4ZTZiNzJhZGExZWQyYzkxOTIwMjMwNzI1MTIxMjAw',
                            Timestamp: timestamp,
                            TransactionType: 'CustomerPayBillOnline',
                            Amount: amount,
                            PartyA: phoneNumber,
                            PartyB: '174379',
                            PhoneNumber: phoneNumber,
                            CallBackURL: `${API_URL}/api/payments/mpesa/callback`,
                            AccountReference: 'Test',
                            TransactionDesc: 'Test Payment'
                        }
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Failed to send STK Push');
                }
                
                resultEl.textContent = JSON.stringify(data, null, 2);
                resultEl.style.display = 'block';
            } catch (error) {
                errorEl.textContent = error.message;
            } finally {
                loadingEl.style.display = 'none';
            }
        });
        
        // Custom API Call
        document.getElementById('customCallBtn').addEventListener('click', async () => {
            const endpoint = document.getElementById('endpoint').value.trim();
            const method = document.getElementById('method').value;
            const auth = document.getElementById('auth').value;
            const dataStr = document.getElementById('data').value.trim();
            const loadingEl = document.getElementById('customLoading');
            const errorEl = document.getElementById('customError');
            const resultEl = document.getElementById('customResult');
            
            if (!endpoint) {
                errorEl.textContent = 'Please enter an endpoint';
                return;
            }
            
            let data;
            try {
                data = dataStr ? JSON.parse(dataStr) : {};
            } catch (error) {
                errorEl.textContent = 'Invalid JSON data';
                return;
            }
            
            try {
                loadingEl.style.display = 'block';
                errorEl.textContent = '';
                resultEl.style.display = 'none';
                
                const response = await fetch(`${API_URL}/api/payments/safaricom-proxy/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        method,
                        auth: auth === 'oauth' ? 'oauth' : undefined,
                        data
                    })
                });
                
                const responseData = await response.json();
                
                if (!response.ok) {
                    throw new Error(responseData.message || 'API call failed');
                }
                
                resultEl.textContent = JSON.stringify(responseData, null, 2);
                resultEl.style.display = 'block';
            } catch (error) {
                errorEl.textContent = error.message;
            } finally {
                loadingEl.style.display = 'none';
            }
        });
    </script>
</body>
</html>
