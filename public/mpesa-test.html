<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M-Pesa Payment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 8px;
            width: 100%;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>M-Pesa Payment Test</h1>
    
    <div class="container">
        <h2>Initiate M-Pesa Payment</h2>
        
        <div class="form-group">
            <label for="phoneNumber">Phone Number (format: 254XXXXXXXXX)</label>
            <input type="text" id="phoneNumber" value="254708374149" placeholder="254XXXXXXXXX">
            <small>Use 254708374149 for successful test payments</small>
        </div>
        
        <div class="form-group">
            <label for="amount">Amount (KES)</label>
            <input type="number" id="amount" value="1" min="1" max="100">
            <small>Use a small amount for testing</small>
        </div>
        
        <button id="initiateBtn">Initiate Payment</button>
        
        <div id="loading" class="loading">Processing...</div>
        
        <div id="error" class="error"></div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>
    
    <div class="container" style="margin-top: 20px; display: none;" id="statusContainer">
        <h2>Payment Status</h2>
        
        <div class="form-group">
            <label for="paymentId">Payment ID</label>
            <input type="text" id="paymentId" readonly>
        </div>
        
        <button id="checkStatusBtn">Check Status</button>
        
        <div id="statusLoading" class="loading">Checking status...</div>
        
        <div id="statusError" class="error"></div>
        
        <div id="statusResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="container" style="margin-top: 20px; display: none;" id="jobsContainer">
        <h2>Run Payment Jobs</h2>
        
        <p>If the callback hasn't been received, you can run the payment jobs to check pending payments.</p>
        
        <button id="runJobsBtn">Run Payment Jobs</button>
        
        <div id="jobsLoading" class="loading">Running jobs...</div>
        
        <div id="jobsError" class="error"></div>
        
        <div id="jobsResult" class="result" style="display: none;"></div>
    </div>
    
    <script>
        // API URL
        const API_URL = 'http://localhost:3000';
        
        // DOM elements
        const initiateBtn = document.getElementById('initiateBtn');
        const phoneNumberInput = document.getElementById('phoneNumber');
        const amountInput = document.getElementById('amount');
        const resultDiv = document.getElementById('result');
        const loadingDiv = document.getElementById('loading');
        const errorDiv = document.getElementById('error');
        
        const statusContainer = document.getElementById('statusContainer');
        const paymentIdInput = document.getElementById('paymentId');
        const checkStatusBtn = document.getElementById('checkStatusBtn');
        const statusResultDiv = document.getElementById('statusResult');
        const statusLoadingDiv = document.getElementById('statusLoading');
        const statusErrorDiv = document.getElementById('statusError');
        
        const jobsContainer = document.getElementById('jobsContainer');
        const runJobsBtn = document.getElementById('runJobsBtn');
        const jobsResultDiv = document.getElementById('jobsResult');
        const jobsLoadingDiv = document.getElementById('jobsLoading');
        const jobsErrorDiv = document.getElementById('jobsError');
        
        // Initiate payment
        initiateBtn.addEventListener('click', async () => {
            const phoneNumber = phoneNumberInput.value.trim();
            const amount = parseInt(amountInput.value);
            
            if (!phoneNumber || !amount) {
                errorDiv.textContent = 'Please fill in all fields';
                return;
            }
            
            try {
                errorDiv.textContent = '';
                resultDiv.style.display = 'none';
                loadingDiv.style.display = 'block';
                
                const response = await fetch(`${API_URL}/api/payments/initiate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userAddress: '0x123456789abcdef',
                        packageId: 'basic',
                        paymentMethod: 'MPESA',
                        amountKES: amount,
                        phoneNumber: phoneNumber
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to initiate payment');
                }
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.style.display = 'block';
                
                // Show status container
                statusContainer.style.display = 'block';
                paymentIdInput.value = data.paymentId;
                
                // Show jobs container
                jobsContainer.style.display = 'block';
                
                // Auto-check status after 10 seconds
                setTimeout(() => {
                    checkStatus(data.paymentId);
                }, 10000);
            } catch (error) {
                errorDiv.textContent = error.message;
            } finally {
                loadingDiv.style.display = 'none';
            }
        });
        
        // Check payment status
        checkStatusBtn.addEventListener('click', () => {
            const paymentId = paymentIdInput.value.trim();
            
            if (!paymentId) {
                statusErrorDiv.textContent = 'Payment ID is required';
                return;
            }
            
            checkStatus(paymentId);
        });
        
        async function checkStatus(paymentId) {
            try {
                statusErrorDiv.textContent = '';
                statusResultDiv.style.display = 'none';
                statusLoadingDiv.style.display = 'block';
                
                const response = await fetch(`${API_URL}/api/payments/status/${paymentId}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to check payment status');
                }
                
                statusResultDiv.textContent = JSON.stringify(data, null, 2);
                statusResultDiv.style.display = 'block';
            } catch (error) {
                statusErrorDiv.textContent = error.message;
            } finally {
                statusLoadingDiv.style.display = 'none';
            }
        }
        
        // Run payment jobs
        runJobsBtn.addEventListener('click', async () => {
            try {
                jobsErrorDiv.textContent = '';
                jobsResultDiv.style.display = 'none';
                jobsLoadingDiv.style.display = 'block';
                
                const response = await fetch(`${API_URL}/api/admin/run-jobs`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        jobType: 'payments'
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to run payment jobs');
                }
                
                jobsResultDiv.textContent = JSON.stringify(data, null, 2);
                jobsResultDiv.style.display = 'block';
                
                // Auto-check status after jobs complete
                const paymentId = paymentIdInput.value.trim();
                if (paymentId) {
                    setTimeout(() => {
                        checkStatus(paymentId);
                    }, 2000);
                }
            } catch (error) {
                jobsErrorDiv.textContent = error.message;
            } finally {
                jobsLoadingDiv.style.display = 'none';
            }
        });
    </script>
</body>
</html>
