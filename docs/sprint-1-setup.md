
# Sprint 1 Setup Guide

**Task 1:** Establish BSC Testnet connectivity, CI/CD pipelines, and prepare for multisig in a later sprint.

---

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Environment Variables](#environment-variables)
4. [BSC Testnet Configuration](#bsc-testnet-configuration)
5. [Smoke-Test Script](#smoke-test-script)
6. [CI/CD Workflows](#cicd-workflows)
7. [Multisig Setup (Deferred)](#multisig-setup-deferred)
8. [Testing](#testing)
9. [Verification & Next Steps](#verification--next-steps)

---

## Overview

In Sprint 1 Task 1 we:

- Connected our services to BSC Testnet
- Implemented a robust web3 service with helper functions
- Added comprehensive tests with coverage thresholds
- Added a smoke-test script to verify network access
- Created GitHub Actions workflows for CI and Testnet deployments
- Deferred multisig provisioning to **Sprint 6** (Mainnet) due to Gnosis Safe Testnet limitations

---

## Prerequisites

- Node.js ≥ 18 and npm installed
- A GitHub account with **write** access to this repo
- Testnet RPC URL, a wallet private key (Testnet), and a Slack webhook URL
- Your local clone of the repo at the **main** branch

---

## Environment Variables

Copy and rename `.env.example` to `.env` in the **root** directory. Populate:

```env
# BSC Testnet RPC
BSC_RPC=https://bsc-testnet.drpc.org
BSC_CHAIN_ID=97

# Wallet for CI deployments (Testnet private key)
PRIVATE_KEY=<your BSC testnet private key>

# Slack notifications
SLACK_WEBHOOK_URL=<your Slack Incoming Webhook URL>
```

Commit **only** `.env.example`; never commit your real keys.

---

## BSC Testnet Configuration

1. **RPC & Chain ID**

   * Confirm `.env` has `BSC_RPC` and `BSC_CHAIN_ID=97`.

2. **Web3 Client**

   * `backend/src/services/web3.ts` reads these variables and instantiates:

     ```ts
     import { createPublicClient, createWalletClient, http } from 'viem';
     import { privateKeyToAccount } from 'viem/accounts';
     import { bscTestnet, bsc } from 'viem/chains';

     // load environment variables
     const {
       BSC_RPC = 'https://bsc-testnet.drpc.org',
       BSC_CHAIN_ID = '97',
       PRIVATE_KEY,                  // your multisig relayer or deployer key
       NODE_ENV = 'development',
     } = process.env;

     // choose chain based on environment
     const chain = NODE_ENV === 'production' ? bsc : bscTestnet;
     const rpcUrl = BSC_RPC;

     // public client for read-only calls
     export const publicClient = createPublicClient({
       chain,
       transport: http(rpcUrl),
     });

     // wallet client for signed transactions
     export const walletClient = createWalletClient({
       chain,
       transport: http(rpcUrl),
       account: PRIVATE_KEY
         ? privateKeyToAccount(`0x${PRIVATE_KEY}`)
         : undefined,
     });
     ```

3. **Helper Functions**

   The web3 service includes helper functions for common blockchain operations:

   ```ts
   // Get current block number
   export async function getBlockNumber() {
     return publicClient.getBlockNumber();
   }

   // Get wallet account nonce
   export async function getAccountNonce() {
     if (!walletClient.account) {
       throw new Error('No PRIVATE_KEY configured for walletClient');
     }
     const nonce = await publicClient.getTransactionCount({
       address: walletClient.account.address,
     });
     return BigInt(nonce);
   }

   // Send a transaction
   export async function sendTx(params: {
     to: `0x${string}`;
     value?: bigint;
     data?: `0x${string}`;
   }) {
     if (!walletClient.account) {
       throw new Error('No PRIVATE_KEY configured for walletClient');
     }
     return walletClient.sendTransaction({
       to: params.to,
       value: params.value,
       data: params.data,
       chain,
       account: walletClient.account,
     });
   }
   ```

4. **Install Dependencies**

   ```bash
   npm install
   ```

   at project root.

---

## Smoke-Test Script

Located at `contracts/scripts/check-connection.ts`:

```ts
import { createPublicClient, http } from "viem";
import { bscTestnet } from "viem/chains";

const client = createPublicClient({
  chain: bscTestnet,
  transport: http(process.env.BSC_RPC)
});

async function main() {
  console.log("Block:", await client.getBlockNumber());
  console.log("Network:", (await client.getChainId()));
}
main();
```

### Running the Smoke Test

```bash
npx ts-node -r dotenv/config contracts/scripts/check-connection.ts
```

Expected output:

```
Block: 51428259n
Network: 97
```

The `n` suffix indicates that the block number is a BigInt value, which is the standard return type for blockchain numeric values in viem.

---

## CI/CD Workflows

Workflow files are in `.github/workflows/`:

* **ci.yml** (on push/PR):

  1. Checkout code
  2. Install deps (`npm ci`)
  3. Run linter (`npm run lint`)
  4. Run tests (`npm test`)
  5. Run smoke-test (`npm run check-connection`)
  6. Notify Slack on pass/fail

* **deploy-testnet.yml** (manual dispatch):

  1. Checkout code
  2. Install deps
  3. Run Hardhat deploy script to Testnet
  4. Verify transaction

### Enabling and Running CI

1. **Add secrets** in GitHub → **Settings → Secrets and variables → Actions**:

   * `BSC_RPC`
   * `PRIVATE_KEY`
   * `SLACK_WEBHOOK_URL`

2. **Open a PR**
   Your branch should trigger **ci.yml** automatically.

3. **Review Logs**

   * Ensure lint, tests, and smoke-test pass.
   * Confirm Slack channel receives “Build Passed” message.

---

## Multisig Setup (Deferred)

Because Gnosis Safe does not yet support BSC Testnet, we will perform multisig provisioning on **Mainnet** during **Sprint 6**. The code scaffolding remains ready:

* Deployment script: `scripts/deploy-multisig.ts`
* Documentation placeholder in `README.md`

---

## Testing

We've implemented comprehensive tests for the web3 service to ensure reliable blockchain connectivity:

### Test Coverage

Located at `backend/tests/web3.test.ts`:

```ts
import {
  publicClient,
  getBlockNumber,
  walletClient,
  getAccountNonce,
  getWalletAddress
} from '../src/services/web3';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

describe('Web3 Service', () => {
  describe('Public Client', () => {
    it('connects to BSC Testnet and returns a block number', async () => {
      const chainId = await publicClient.getChainId();
      expect(chainId).toBe(97);

      const block = await getBlockNumber();
      expect(typeof block).toBe('bigint');
      expect(block > 0n).toBe(true);
    });

    it('handles RPC connection failures gracefully', async () => {
      // Test with invalid RPC URL
      const badClient = createPublicClient({
        chain: bscTestnet,
        transport: http('https://invalid-rpc-url.example.com'),
      });
      await expect(badClient.getBlockNumber()).rejects.toThrow();
    });
  });

  describe('Wallet Client', () => {
    // Tests for wallet functionality
    it('can fetch account nonce when PRIVATE_KEY is provided', async () => {
      const nonce = await getAccountNonce();
      expect(typeof nonce).toBe('bigint');
      expect(nonce >= 0n).toBe(true);
    });

    // Error handling tests
    it('throws error when trying to get wallet address without PRIVATE_KEY', async () => {
      // Test with no private key
    });
  });
});
```

### Coverage Thresholds

We've configured Jest to enforce coverage thresholds to ensure code quality:

```js
// jest.config.js
module.exports = {
  // ...
  coverageThreshold: {
    global: {
      branches: 60,
      functions: 80,
      lines: 80,
      statements: 80
    },
    'backend/src/services/web3.ts': {
      branches: 60,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

### Running Tests

```bash
npm test
```

For coverage reports:

```bash
npm test -- --coverage
```

---

## Verification & Next Steps

* ✅ Smoke-test passed locally and in CI
* ✅ Web3 service implemented with comprehensive tests
* ✅ CI workflows triggered on PR and notified Slack
* ✅ Test coverage thresholds established to catch regressions
* ➡️ **Next Task:** Sprint 1 Task 2 – *Design payment-gateway API & reconciliation flows*

Once your PR is merged into **main**, mark **Task 1** complete in ClickUp and begin Task 2.
