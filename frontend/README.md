# BlockCoop - DeFi-Enabled SACCO Platform

A modern decentralized autonomous cooperative organization (DACO) platform built with React, TypeScript, and Web3 technologies.

## Features

- 🔐 **Multi-Wallet Support** - Connect with MetaMask, WalletConnect, Coinbase Wallet, and more
- 📧 **Email & Social Login** - Sign in with email, Google, X, GitHub, Discord, or Apple
- 💰 **Investment Packages** - Multiple vesting schedules with competitive APY rates
- 📊 **Analytics Dashboard** - Real-time portfolio tracking and performance metrics
- 🏛️ **Governance** - Community-driven decision making
- 🔒 **Security First** - Audited smart contracts and multi-signature wallets

## Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd blockcoop-defi-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Reown AppKit**
   - Visit [Reown Cloud](https://cloud.reown.com) to create a project
   - Copy your Project ID
   - Create a `.env` file based on `.env.example`
   - Add your Project ID to the `.env` file:
     ```
     VITE_REOWN_PROJECT_ID=your_project_id_here
     ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

## Wallet Integration

This project uses Reown AppKit (formerly WalletConnect) with wagmi for comprehensive wallet connectivity:

### Supported Connection Methods
- **Crypto Wallets**: MetaMask, Coinbase Wallet, Trust Wallet, Rainbow, and 300+ others
- **Email Login**: Connect with any email address
- **Social Logins**: Google, X (Twitter), GitHub, Discord, Apple
- **Multi-Chain**: Ethereum, Polygon, Arbitrum, Base, Optimism

### Key Features
- Seamless wallet switching
- Account management
- Transaction signing
- Multi-chain support
- Social recovery options

## Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Animations**: Framer Motion
- **Charts**: Recharts
- **Web3**: wagmi, viem, Reown AppKit
- **State Management**: TanStack Query
- **Icons**: Lucide React
- **Build Tool**: Vite

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── home/           # Homepage sections
│   ├── layout/         # Layout components
│   ├── purchase/       # Investment flow components
│   ├── ui/             # Base UI components
│   └── wallet/         # Wallet connection components
├── config/             # Configuration files
│   └── wagmi.ts        # Wallet & blockchain config
├── hooks/              # Custom React hooks
├── pages/              # Page components
├── types/              # TypeScript type definitions
└── main.tsx           # Application entry point
```

## Environment Variables

Create a `.env` file in the root directory:

```env
# Required
VITE_REOWN_PROJECT_ID=your_project_id_here

# Optional - Custom RPC URLs
VITE_MAINNET_RPC_URL=your_mainnet_rpc
VITE_POLYGON_RPC_URL=your_polygon_rpc
VITE_ARBITRUM_RPC_URL=your_arbitrum_rpc
```

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.