import { useAccount, useDisconnect, useBalance } from 'wagmi';
import { useAppKit } from '@reown/appkit/react';
import { User } from '../types';

export const useWallet = () => {
  const { address, isConnected, isConnecting } = useAccount();
  const { disconnect } = useDisconnect();
  const { open } = useAppKit();
  
  // Get balance for the connected account
  const { data: balance } = useBalance({
    address: address,
  });

  // Create user object from wallet data
  const user: User | null = isConnected && address ? {
    id: address,
    address: address,
    balance: balance ? parseFloat(balance.formatted) : 0,
    vestedTokens: 8750.00, // This would come from your smart contract
    totalPurchased: 12000.00, // This would come from your smart contract
    referralCode: 'BLC12345', // This would come from your backend
    referralCount: 3, // This would come from your backend
    isConnected: true,
  } : null;

  const connectWallet = async () => {
    try {
      await open();
    } catch (error) {
      console.error('Failed to connect wallet:', error);
      throw error;
    }
  };

  const disconnectWallet = () => {
    disconnect();
  };

  return {
    user,
    isConnecting,
    error: null, // Appkit handles errors internally
    connectWallet,
    disconnectWallet,
    isConnected,
    address,
    balance: balance?.formatted || '0',
  };
};