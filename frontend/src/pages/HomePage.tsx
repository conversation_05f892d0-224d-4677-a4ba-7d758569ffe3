import React from 'react';
import { HeroSection } from '../components/home/<USER>';
import { StatsSection } from '../components/home/<USER>';
import { FeaturesSection } from '../components/home/<USER>';

interface HomePageProps {
  onNavigate: (page: string) => void;
}

export const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => {
  return (
    <div className="min-h-screen">
      <HeroSection onNavigate={onNavigate} />
      <StatsSection />
      <FeaturesSection />
    </div>
  );
};