import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Menu, X, Wallet, User, Settings, LogOut } from 'lucide-react';
import { useAppKit } from '@reown/appkit/react';
import { Button } from '../ui/Button';
import { useWallet } from '../../hooks/useWallet';
import { WalletModal } from '../wallet/WalletModal';

interface HeaderProps {
  onNavigate: (page: string) => void;
  currentPage: string;
}

export const Header: React.FC<HeaderProps> = ({ onNavigate, currentPage }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, isConnected, disconnectWallet } = useWallet();
  const { open } = useAppKit();

  const navigation = [
    { name: 'Home', id: 'home' },
    { name: 'Purchase', id: 'purchase' },
    { name: 'Dashboard', id: 'dashboard' },
    { name: 'Admin', id: 'admin' },
  ];

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleWalletConnect = async () => {
    try {
      await open();
    } catch (error) {
      console.error('Failed to open wallet modal:', error);
    }
  };

  return (
    <>
      <header className="bg-white shadow-sm border-b border-dark-100 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-2 cursor-pointer"
                onClick={() => onNavigate('home')}
              >
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-success-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-heading font-bold text-sm">BC</span>
                </div>
                <span className="font-heading font-bold text-xl text-dark-900">BlockCoop</span>
              </motion.div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8">
              {navigation.map((item) => (
                <button
                  key={item.id}
                  onClick={() => onNavigate(item.id)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentPage === item.id
                      ? 'text-primary-600 bg-primary-50'
                      : 'text-dark-600 hover:text-primary-600 hover:bg-primary-50'
                  }`}
                >
                  {item.name}
                </button>
              ))}
            </nav>

            {/* Wallet & User Actions */}
            <div className="flex items-center space-x-4">
              {isConnected ? (
                <div className="relative">
                  <button
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    className="flex items-center space-x-2 px-3 py-2 bg-primary-50 text-primary-700 rounded-lg hover:bg-primary-100 transition-colors"
                  >
                    <Wallet className="w-4 h-4" />
                    <span className="hidden sm:block text-sm font-medium">
                      {formatAddress(user!.address)}
                    </span>
                  </button>

                  {isUserMenuOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-dark-100 py-1"
                    >
                      <button
                        onClick={() => {
                          onNavigate('dashboard');
                          setIsUserMenuOpen(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-dark-700 hover:bg-dark-50"
                      >
                        <User className="w-4 h-4 mr-3" />
                        Dashboard
                      </button>
                      <button
                        onClick={() => {
                          handleWalletConnect();
                          setIsUserMenuOpen(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-dark-700 hover:bg-dark-50"
                      >
                        <Settings className="w-4 h-4 mr-3" />
                        Wallet Settings
                      </button>
                      <hr className="my-1 border-dark-100" />
                      <button
                        onClick={() => {
                          disconnectWallet();
                          setIsUserMenuOpen(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <LogOut className="w-4 h-4 mr-3" />
                        Disconnect
                      </button>
                    </motion.div>
                  )}
                </div>
              ) : (
                <Button
                  onClick={handleWalletConnect}
                  className="hidden sm:flex"
                >
                  <Wallet className="w-4 h-4 mr-2" />
                  Connect Wallet
                </Button>
              )}

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 rounded-md text-dark-600 hover:text-primary-600 hover:bg-primary-50"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-dark-100"
          >
            <div className="px-4 py-4 space-y-2">
              {navigation.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    onNavigate(item.id);
                    setIsMenuOpen(false);
                  }}
                  className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    currentPage === item.id
                      ? 'text-primary-600 bg-primary-50'
                      : 'text-dark-600 hover:text-primary-600 hover:bg-primary-50'
                  }`}
                >
                  {item.name}
                </button>
              ))}
              {!isConnected && (
                <Button
                  onClick={() => {
                    handleWalletConnect();
                    setIsMenuOpen(false);
                  }}
                  className="w-full mt-4"
                >
                  <Wallet className="w-4 h-4 mr-2" />
                  Connect Wallet
                </Button>
              )}
            </div>
          </motion.div>
        )}
      </header>

      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
      />
    </>
  );
};