import React from 'react';
import { motion } from 'framer-motion';
import { Check, Star } from 'lucide-react';
import { Package } from '../../types';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

interface PackageCardProps {
  package: Package;
  onSelect: (packageId: string) => void;
  selected?: boolean;
}

export const PackageCard: React.FC<PackageCardProps> = ({ 
  package: pkg, 
  onSelect, 
  selected = false 
}) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`relative ${selected ? 'ring-2 ring-primary-500' : ''}`}
    >
      {pkg.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-primary-500 to-success-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
            <Star className="w-4 h-4 mr-1" />
            Most Popular
          </div>
        </div>
      )}
      
      <Card className={`h-full ${pkg.popular ? 'border-primary-200 bg-primary-50/30' : ''}`}>
        <div className="text-center mb-6">
          <h3 className="text-2xl font-heading font-bold text-dark-900 mb-2">
            {pkg.name}
          </h3>
          <p className="text-dark-600 mb-4">{pkg.description}</p>
          
          <div className="space-y-2">
            <div className="flex items-center justify-center space-x-2">
              <span className="text-sm text-dark-500">Min:</span>
              <span className="text-lg font-semibold text-dark-900">
                ${pkg.minAmount.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <span className="text-sm text-dark-500">Max:</span>
              <span className="text-lg font-semibold text-dark-900">
                ${pkg.maxAmount.toLocaleString()}
              </span>
            </div>
          </div>
          
          <div className="mt-4 p-4 bg-success-50 rounded-lg">
            <div className="text-3xl font-heading font-bold text-success-600 mb-1">
              {pkg.interestRate}% APY
            </div>
            <div className="text-sm text-success-700">
              {pkg.vestingPeriod} months vesting
            </div>
          </div>
        </div>

        <div className="space-y-3 mb-6">
          {pkg.features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-3">
              <Check className="w-5 h-5 text-success-500 flex-shrink-0" />
              <span className="text-dark-700">{feature}</span>
            </div>
          ))}
        </div>

        <Button
          onClick={() => onSelect(pkg.id)}
          variant={selected ? 'primary' : pkg.popular ? 'primary' : 'outline'}
          className="w-full"
        >
          {selected ? 'Selected' : 'Select Package'}
        </Button>
      </Card>
    </motion.div>
  );
};