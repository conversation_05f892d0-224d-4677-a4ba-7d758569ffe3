import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Smartphone, Wallet } from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

interface PaymentFormProps {
  selectedPackage: any;
  onPayment: (data: any) => void;
}

export const PaymentForm: React.FC<PaymentFormProps> = ({ selectedPackage, onPayment }) => {
  const [paymentMethod, setPaymentMethod] = useState<'usdt' | 'mpesa'>('usdt');
  const [amount, setAmount] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [mpesaPhone, setMpesaPhone] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    onPayment({
      amount: parseFloat(amount),
      method: paymentMethod,
      referralCode,
      phone: mpesaPhone,
    });
    
    setLoading(false);
  };

  const paymentMethods = [
    {
      id: 'usdt',
      name: 'USDT (Crypto)',
      description: 'Pay with USDT cryptocurrency',
      icon: Wallet,
    },
    {
      id: 'mpesa',
      name: 'M-Pesa',
      description: 'Pay with M-Pesa mobile money',
      icon: Smartphone,
    },
  ];

  return (
    <Card>
      <h3 className="text-xl font-heading font-semibold text-dark-900 mb-6">
        Payment Details
      </h3>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Payment Method Selection */}
        <div>
          <label className="block text-sm font-medium text-dark-700 mb-3">
            Payment Method
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {paymentMethods.map((method) => (
              <motion.button
                key={method.id}
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setPaymentMethod(method.id as any)}
                className={`p-4 border rounded-lg transition-all ${
                  paymentMethod === method.id
                    ? 'border-primary-500 bg-primary-50 text-primary-900'
                    : 'border-dark-200 hover:border-primary-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <method.icon className="w-6 h-6" />
                  <div className="text-left">
                    <h4 className="font-medium">{method.name}</h4>
                    <p className="text-sm text-dark-600">{method.description}</p>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Amount Input */}
        <Input
          label="Investment Amount (USD)"
          type="number"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder={`Min: $${selectedPackage?.minAmount || 0}`}
          required
          min={selectedPackage?.minAmount || 0}
          max={selectedPackage?.maxAmount || 100000}
        />

        {/* M-Pesa Phone (conditional) */}
        {paymentMethod === 'mpesa' && (
          <Input
            label="M-Pesa Phone Number"
            type="tel"
            value={mpesaPhone}
            onChange={(e) => setMpesaPhone(e.target.value)}
            placeholder="+254 712 345 678"
            required
          />
        )}

        {/* Referral Code */}
        <Input
          label="Referral Code (Optional)"
          value={referralCode}
          onChange={(e) => setReferralCode(e.target.value)}
          placeholder="Enter referral code"
          helper="Get 5% bonus on your investment with a valid referral code"
        />

        {/* Investment Summary */}
        {amount && (
          <Card className="bg-dark-50">
            <h4 className="font-medium text-dark-900 mb-3">Investment Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-dark-600">Package:</span>
                <span className="font-medium">{selectedPackage?.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-dark-600">Amount:</span>
                <span className="font-medium">${parseFloat(amount || '0').toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-dark-600">APY:</span>
                <span className="font-medium text-success-600">{selectedPackage?.interestRate}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-dark-600">Vesting Period:</span>
                <span className="font-medium">{selectedPackage?.vestingPeriod} months</span>
              </div>
              {referralCode && (
                <div className="flex justify-between text-success-600">
                  <span>Referral Bonus:</span>
                  <span className="font-medium">+5%</span>
                </div>
              )}
            </div>
          </Card>
        )}

        <Button
          type="submit"
          loading={loading}
          disabled={!amount || parseFloat(amount) < (selectedPackage?.minAmount || 0)}
          className="w-full"
          size="lg"
        >
          {loading ? 'Processing Payment...' : `Invest $${parseFloat(amount || '0').toLocaleString()}`}
        </Button>
      </form>
    </Card>
  );
};