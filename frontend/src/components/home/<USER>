import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, TrendingUp, Shield, Users } from 'lucide-react';
import { Button } from '../ui/Button';
import { useWallet } from '../../hooks/useWallet';
import { useAppKit } from '@reown/appkit/react';

interface HeroSectionProps {
  onNavigate: (page: string) => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ onNavigate }) => {
  const { isConnected } = useWallet();
  const { open } = useAppKit();

  const handleAction = async (targetPage: string) => {
    if (!isConnected) {
      try {
        await open();
      } catch (error) {
        console.error('Failed to open wallet modal:', error);
      }
    } else {
      onNavigate(targetPage);
    }
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-success-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-primary-500 rounded-full animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-success-500 rounded-full animate-bounce-slow"></div>
        <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-primary-400 rounded-full animate-pulse"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Next-Generation DeFi SACCO
              </motion.div>

              <h1 className="text-4xl lg:text-6xl font-heading font-bold text-dark-900 leading-tight">
                Revolutionize Your{' '}
                <span className="bg-gradient-to-r from-primary-600 to-success-600 bg-clip-text text-transparent">
                  Financial Future
                </span>
              </h1>

              <p className="text-xl text-dark-600 leading-relaxed">
                Join BlockCoop's decentralized autonomous cooperative organization. 
                Stake, earn, and grow your wealth through transparent, community-driven financial services.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                onClick={() => handleAction('purchase')}
                className="group"
              >
                Start Investing
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => handleAction('dashboard')}
              >
                View Dashboard
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 border-t border-dark-100">
              <div className="text-center">
                <h3 className="text-2xl font-heading font-bold text-dark-900">$2.5M+</h3>
                <p className="text-dark-600 text-sm">Total Value Locked</p>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-heading font-bold text-dark-900">1,250+</h3>
                <p className="text-dark-600 text-sm">Active Members</p>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-heading font-bold text-dark-900">15%</h3>
                <p className="text-dark-600 text-sm">Average APY</p>
              </div>
            </div>
          </motion.div>

          {/* Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="relative">
              {/* Main Card */}
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="bg-white rounded-2xl shadow-2xl p-8 border border-dark-100"
              >
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="font-heading font-semibold text-dark-900">Portfolio Value</h3>
                    <div className="flex items-center text-success-600">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      <span className="text-sm font-medium">+12.5%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h2 className="text-3xl font-heading font-bold text-dark-900">$8,750.00</h2>
                    <p className="text-dark-600">Total Balance</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-primary-50 rounded-lg p-4">
                      <h4 className="font-medium text-primary-900">Staked</h4>
                      <p className="text-2xl font-bold text-primary-600">$6,200</p>
                    </div>
                    <div className="bg-success-50 rounded-lg p-4">
                      <h4 className="font-medium text-success-900">Rewards</h4>
                      <p className="text-2xl font-bold text-success-600">$2,550</p>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
                className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-primary-500 to-success-500 rounded-full flex items-center justify-center"
              >
                <Shield className="w-8 h-8 text-white" />
              </motion.div>

              <motion.div
                animate={{ y: [0, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-br from-success-500 to-primary-500 rounded-full flex items-center justify-center"
              >
                <Users className="w-10 h-10 text-white" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
