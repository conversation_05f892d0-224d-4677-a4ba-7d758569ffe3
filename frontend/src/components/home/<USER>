import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import { TrendingUp, Users, DollarSign, Shield } from 'lucide-react';
import { Card } from '../ui/Card';

const chartData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 600 },
  { name: 'Mar', value: 800 },
  { name: 'Apr', value: 1200 },
  { name: 'May', value: 1800 },
  { name: 'Jun', value: 2500 },
];

export const StatsSection: React.FC = () => {
  const stats = [
    {
      title: 'Total Value Locked',
      value: '$2,547,890',
      change: '+15.3%',
      icon: DollarSign,
      color: 'primary',
    },
    {
      title: 'Active Members',
      value: '1,247',
      change: '+8.2%',
      icon: Users,
      color: 'success',
    },
    {
      title: 'Total Transactions',
      value: '12,847',
      change: '+23.1%',
      icon: TrendingUp,
      color: 'primary',
    },
    {
      title: 'Security Score',
      value: '99.9%',
      change: '+0.1%',
      icon: Shield,
      color: 'success',
    },
  ];

  return (
    <section className="py-20 bg-dark-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl lg:text-4xl font-heading font-bold text-dark-900 mb-4">
            Platform Performance
          </h2>
          <p className="text-xl text-dark-600">
            Real-time insights into our thriving DeFi ecosystem
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card hover className="text-center">
                <div className={`w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center ${
                  stat.color === 'primary' ? 'bg-primary-100' : 'bg-success-100'
                }`}>
                  <stat.icon className={`w-6 h-6 ${
                    stat.color === 'primary' ? 'text-primary-600' : 'text-success-600'
                  }`} />
                </div>
                <h3 className="text-2xl font-heading font-bold text-dark-900 mb-1">
                  {stat.value}
                </h3>
                <p className="text-dark-600 mb-2">{stat.title}</p>
                <div className={`inline-flex items-center text-sm font-medium ${
                  stat.color === 'primary' ? 'text-primary-600' : 'text-success-600'
                }`}>
                  <TrendingUp className="w-4 h-4 mr-1" />
                  {stat.change}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Chart */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card>
            <div className="mb-6">
              <h3 className="text-xl font-heading font-semibold text-dark-900 mb-2">
                TVL Growth Over Time
              </h3>
              <p className="text-dark-600">Monthly total value locked in thousands (USD)</p>
            </div>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  <XAxis 
                    dataKey="name" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: '#6B7280', fontSize: 12 }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: '#6B7280', fontSize: 12 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="#4F46E5" 
                    strokeWidth={3}
                    dot={{ fill: '#4F46E5', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#4F46E5', strokeWidth: 2, fill: '#fff' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};