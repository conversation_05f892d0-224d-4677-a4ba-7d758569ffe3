import React from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  TrendingUp, 
  Users, 
  Zap, 
  Award, 
  Globe,
  Lock,
  BarChart3 
} from 'lucide-react';
import { Card } from '../ui/Card';

export const FeaturesSection: React.FC = () => {
  const features = [
    {
      icon: Shield,
      title: 'Secure & Transparent',
      description: 'Smart contracts audited by leading security firms ensure your funds are always protected.',
      color: 'primary',
    },
    {
      icon: TrendingUp,
      title: 'High Yield Returns',
      description: 'Earn competitive returns through our optimized DeFi strategies and automated yield farming.',
      color: 'success',
    },
    {
      icon: Users,
      title: 'Community Driven',
      description: 'Participate in governance decisions and shape the future of our cooperative platform.',
      color: 'primary',
    },
    {
      icon: Zap,
      title: 'Instant Transactions',
      description: 'Lightning-fast processing with minimal fees powered by advanced blockchain technology.',
      color: 'success',
    },
    {
      icon: Lock,
      title: 'Flexible Vesting',
      description: 'Multiple vesting schedules to match your investment timeline and financial goals.',
      color: 'primary',
    },
    {
      icon: BarChart3,
      title: 'Analytics Dashboard',
      description: 'Comprehensive insights and real-time tracking of your portfolio performance.',
      color: 'success',
    },
  ];

  const benefits = [
    { icon: Award, text: 'Industry-leading APY rates' },
    { icon: Globe, text: 'Global accessibility 24/7' },
    { icon: Shield, text: 'Insurance fund protection' },
    { icon: Users, text: 'Community-first approach' },
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl lg:text-4xl font-heading font-bold text-dark-900 mb-4">
            Why Choose BlockCoop?
          </h2>
          <p className="text-xl text-dark-600 max-w-3xl mx-auto">
            Experience the next generation of decentralized finance with features designed 
            for both beginners and experienced DeFi users.
          </p>
        </motion.div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card hover className="h-full">
                <div className={`w-12 h-12 mb-6 rounded-lg flex items-center justify-center ${
                  feature.color === 'primary' ? 'bg-primary-100' : 'bg-success-100'
                }`}>
                  <feature.icon className={`w-6 h-6 ${
                    feature.color === 'primary' ? 'text-primary-600' : 'text-success-600'
                  }`} />
                </div>
                <h3 className="text-xl font-heading font-semibold text-dark-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-dark-600 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Benefits Bar */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-r from-primary-500 to-success-500 text-white">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit.text}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center space-x-3"
                >
                  <benefit.icon className="w-6 h-6 flex-shrink-0" />
                  <span className="font-medium">{benefit.text}</span>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};