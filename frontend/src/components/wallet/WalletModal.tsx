import React from 'react';
import { useAppKit } from '@reown/appkit/react';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { Wallet, Shield, Zap, Users, Mail, Globe } from 'lucide-react';

interface WalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const WalletModal: React.FC<WalletModalProps> = ({ isOpen, onClose }) => {
  const { open } = useAppKit();

  const handleConnect = async () => {
    try {
      await open();
      onClose();
    } catch (error) {
      console.error('Failed to open wallet modal:', error);
    }
  };

  const features = [
    {
      icon: Wallet,
      title: 'Multiple Wallets',
      description: 'MetaMask, WalletConnect, Coinbase & more'
    },
    {
      icon: Mail,
      title: 'Email Login',
      description: 'Connect with just your email address'
    },
    {
      icon: Users,
      title: 'Social Logins',
      description: 'Google, X, GitHub, Discord & Apple'
    },
    {
      icon: Shield,
      title: 'Secure',
      description: 'Industry-standard security protocols'
    },
    {
      icon: Zap,
      title: 'Fast',
      description: 'Quick and seamless connection'
    },
    {
      icon: Globe,
      title: 'Multi-Chain',
      description: 'Ethereum, Polygon, Arbitrum & more'
    }
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Connect to BlockCoop">
      <div className="space-y-6">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
            <Wallet className="w-8 h-8 text-primary-600" />
          </div>
          <p className="text-dark-600 mb-6">
            Connect your wallet or sign in with email/social to start participating in BlockCoop's DeFi ecosystem
          </p>
          
          <Button onClick={handleConnect} size="lg" className="w-full mb-6">
            <Wallet className="w-5 h-5 mr-2" />
            Connect Wallet
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 bg-dark-50 rounded-lg">
              <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <feature.icon className="w-4 h-4 text-primary-600" />
              </div>
              <div>
                <h4 className="font-medium text-dark-900 text-sm">{feature.title}</h4>
                <p className="text-xs text-dark-600">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center pt-4 border-t border-dark-100">
          <p className="text-xs text-dark-500">
            By connecting, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </Modal>
  );
};