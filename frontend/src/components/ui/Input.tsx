import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helper?: string;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helper,
  className = '',
  ...props
}) => {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-dark-700">
          {label}
        </label>
      )}
      <input
        className={`
          w-full px-4 py-2.5 border border-dark-200 rounded-lg
          focus:ring-2 focus:ring-primary-500 focus:border-primary-500
          transition-colors duration-200
          ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
          ${className}
        `}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
      {helper && !error && (
        <p className="text-sm text-dark-500">
          {helper}
        </p>
      )}
    </div>
  );
};