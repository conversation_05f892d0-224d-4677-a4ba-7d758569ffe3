export interface User {
  id: string;
  address: string;
  balance: number;
  vestedTokens: number;
  totalPurchased: number;
  referralCode: string;
  referralCount: number;
  isConnected: boolean;
}

export interface Package {
  id: string;
  name: string;
  minAmount: number;
  maxAmount: number;
  vestingPeriod: number;
  interestRate: number;
  description: string;
  features: string[];
  popular?: boolean;
}

export interface Transaction {
  id: string;
  type: 'purchase' | 'claim' | 'referral';
  amount: number;
  token: string;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
  hash?: string;
}

export interface VestingSchedule {
  id: string;
  packageId: string;
  totalAmount: number;
  claimedAmount: number;
  nextClaimDate: Date;
  claimableAmount: number;
  completionDate: Date;
}

export interface AdminStats {
  totalUsers: number;
  totalValueLocked: number;
  totalTransactions: number;
  activePackages: number;
  pendingWithdrawals: number;
}