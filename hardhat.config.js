require("dotenv").config();
require("@nomicfoundation/hardhat-toolbox");
require("@nomicfoundation/hardhat-verify");

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  defaultNetwork: "hardhat",

  solidity: {
    compilers: [
      {
        version: "0.8.19",
        settings: { optimizer: { enabled: true, runs: 200 } },
      },
      {
        version: "0.6.6",
        settings: { optimizer: { enabled: true, runs: 200 } },
      },
      {
        version: "0.5.16",
        settings: { optimizer: { enabled: true, runs: 200 } },
      },
    ],
    overrides: {
      "@uniswap/v2-core/**":      { version: "0.5.16", settings: {} },
      "@uniswap/v2-periphery/**": { version: "0.6.6",  settings: {} },
      "@uniswap/lib/**":          { version: "0.6.6",  settings: {} },
    },
  },

  networks: {
    hardhat: { chainId: 1337 },
    bscTestnet: {
      url: process.env.BSC_TESTNET_RPC_URL || "",
      accounts: process.env.PRIVATE_KEY ? [`0x${process.env.PRIVATE_KEY}`] : [],
      chainId: 97,
      gasPrice: 5 * 1e9,    // 5 gwei
    },
    bscMainnet: {
      url: process.env.BSC_MAINNET_RPC_URL || "",
      accounts: process.env.PRIVATE_KEY ? [`0x${process.env.PRIVATE_KEY}`] : [],
      chainId: 56,
    },
  },

  paths: {
    sources:   "./contracts/src",
    tests:     "./contracts/test",
    scripts:   "./scripts",
    cache:     "./cache",
    artifacts: "./artifacts",
  },

  mocha: {
    timeout: 20000,
  },

  etherscan: {
    apiKey: {
      bsc:        process.env.BSCSCAN_API_KEY || "",
      bscTestnet: process.env.BSCSCAN_API_KEY || "",
    },
    customChains: [
      {
        network: "bscTestnet",
        chainId: 97,
        urls: {
          apiURL:     "https://api-testnet.bscscan.com/api",  
          browserURL: "https://testnet.bscscan.com"
        }
      }
    ]
  },
};
