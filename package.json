{"devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.8", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.13", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^22.15.17", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "chai": "^4.5.0", "dotenv": "^16.5.0", "ethers": "^6.14.1", "hardhat": "^2.24.0", "inquirer": "^12.6.2", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "scripts": {"test": "jest --passWithNoTests", "test:coverage": "jest --coverage", "start": "ts-node -r dotenv/config backend/src/index.ts", "dev": "nodemon --exec ts-node -r dotenv/config backend/src/index.ts", "check-connection": "ts-node -r dotenv/config contracts/scripts/check-connection.ts", "run-payment-jobs": "ts-node -r dotenv/config backend/src/jobs/runJobs.ts"}, "dependencies": {"@openzeppelin/contracts": "^4.9.0", "@uniswap/lib": "^4.0.1-alpha", "@uniswap/v2-core": "^1.0.1", "@uniswap/v2-periphery": "^1.1.0-beta.0", "axios": "^1.6.7", "express": "^4.21.2", "express-rate-limit": "^7.2.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "uuid": "^9.0.1", "viem": "^2.29.0", "zod": "^3.22.4"}}