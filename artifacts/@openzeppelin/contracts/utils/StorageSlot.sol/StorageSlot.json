{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212200d7c79cc0d2dc5a13cd1f9ec7db59a64db211d68e514f0971fff90f2f3fff0a564736f6c63430008130033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212200d7c79cc0d2dc5a13cd1f9ec7db59a64db211d68e514f0971fff90f2f3fff0a564736f6c63430008130033", "linkReferences": {}, "deployedLinkReferences": {}}