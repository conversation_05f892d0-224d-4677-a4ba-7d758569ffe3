{"_format": "hh-sol-artifact-1", "contractName": "ERC2771Context", "sourceName": "@openzeppelin/contracts/metatx/ERC2771Context.sol", "abi": [{"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}], "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}