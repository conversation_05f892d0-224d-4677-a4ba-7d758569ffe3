{"_format": "hh-sol-artifact-1", "contractName": "LPToken", "sourceName": "contracts/src/LPToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "minter", "type": "address"}, {"internalType": "address", "name": "burner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "BURNER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}