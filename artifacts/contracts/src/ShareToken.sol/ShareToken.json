{"_format": "hh-sol-artifact-1", "contractName": "ShareToken", "sourceName": "contracts/src/ShareToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "address", "name": "multisig", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pair", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "AMMPairUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FeesDistributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "key", "type": "bytes32"}, {"indexed": false, "internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"indexed": false, "internalType": "address", "name": "wallet", "type": "address"}], "name": "TaxBucketUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTISIG_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TAX_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "bucketKeys", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "buckets", "outputs": [{"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "wallet", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "listAMMPairs", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "listTaxBuckets", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setAMMPair", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}, {"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "wallet", "type": "address"}], "name": "setTaxBucket", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalFeeBps", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}