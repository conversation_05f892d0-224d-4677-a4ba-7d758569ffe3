{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceName": "contracts/src/VestingVault.sol", "abi": [{"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}, {"internalType": "address", "name": "shareToken_", "type": "address"}, {"internalType": "address", "name": "lpToken_", "type": "address"}, {"internalType": "address", "name": "locker", "type": "address"}, {"internalType": "uint32", "name": "cliff_", "type": "uint32"}, {"internalType": "uint32", "name": "duration_", "type": "uint32"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Claimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "cliff", "type": "uint32"}, {"indexed": false, "internalType": "uint32", "name": "duration", "type": "uint32"}], "name": "DefaultParamsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "start", "type": "uint256"}], "name": "Locked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LOCKER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "claim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "claimable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "claimed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "defaultCliff", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "defaultDuration", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getLocks", "outputs": [{"components": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "start", "type": "uint256"}, {"internalType": "uint32", "name": "cliff", "type": "uint32"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}], "internalType": "struct VestingVault.Lock[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}], "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "lock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "lpToken", "outputs": [{"internalType": "contract ILPToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "cliff_", "type": "uint32"}, {"internalType": "uint32", "name": "duration_", "type": "uint32"}], "name": "setDefaultParams", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "shareToken", "outputs": [{"internalType": "contract IShareToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "vestedOf", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}