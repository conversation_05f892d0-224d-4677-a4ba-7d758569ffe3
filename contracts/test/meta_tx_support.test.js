// test/meta_tx_support.test.js
const { expect } = require("chai");
const { ethers } = require("hardhat");
const { keccak256, defaultAbiCoder, toUtf8Bytes, solidityPack } = ethers.utils;

describe("Meta-tx support for PackageManager, VestingVault, DividendDistributor", function() {
  let forwarder, pm, vv, dd;
  let owner, user, referrer;
  let USDT;

  // helper: build domain
  function getDomain(forwarderAddress, chainId) {
    return {
      name: "MinimalForwarder",
      version: "0.0.1",
      chainId,
      verifyingContract: forwarderAddress
    };
  }

  // helper: build typed data and signature
  async function signRequest(request, signer) {
    const domain = getDomain(forwarder.address, await signer.getChainId());
    const types = { ForwardRequest: [
      { name: 'from', type: 'address' },
      { name: 'to',   type: 'address' },
      { name: 'value',type: 'uint256' },
      { name: 'gas',  type: 'uint256' },
      { name: 'nonce',type: 'uint256' },
      { name: 'data', type: 'bytes' }
    ]};
    return signer._signTypedData(domain, types, request);
  }

  beforeEach(async () => {
    [owner, user, referrer] = await ethers.getSigners();
    const FW = await ethers.getContractFactory("MinimalForwarder");
    forwarder = await FW.deploy();
    await forwarder.deployed();

    // minimal ERC20 share token
    const ShareToken = await ethers.getContractFactory("ShareToken");
    const shareToken = await ShareToken.deploy("S", "S", owner.address);
    // USDT mock
    const Tether = await ethers.getContractFactory("BlockCoopTestTether");
    USDT = await Tether.deploy();

    // Deploy PackageManager with forwarder
    const PM = await ethers.getContractFactory("PackageManager");
    pm = await PM.deploy(
      forwarder.address,
      USDT.address,
      shareToken.address,
      shareToken.address,
      shareToken.address,
      shareToken.address,
      owner.address,
      owner.address,
      owner.address
    );
    await pm.deployed();

    // VestingVault
    const VV = await ethers.getContractFactory("VestingVault");
    vv = await VV.deploy(
      forwarder.address,
      shareToken.address,
      shareToken.address,
      pm.address,
      0,
      100,
      owner.address
    );
    await vv.deployed();

    // DividendDistributor
    const DD = await ethers.getContractFactory("DividendDistributor");
    dd = await DD.deploy(
      forwarder.address,
      shareToken.address,
      USDT.address
    );
    await dd.deployed();
  });

  it("should relay purchase via forwarder meta-tx", async () => {
    // add a package
    await pm.connect(owner).addPackage(1, 1000, 10000, 5000, 1000);
    // mint USDT to user
    await USDT.mint(user.address, 1000);
    // approve via meta-tx
    const approveData = USDT.interface.encodeFunctionData("approve", [pm.address, 1000]);
    const nonce = await forwarder.getNonce(user.address);
    const req = {
      from: user.address,
      to: pm.address,
      value: 0,
      gas: 1e6,
      nonce: nonce.toNumber(),
      data: approveData
    };
    const sig = await signRequest(req, user);
    await forwarder.connect(owner).execute(req, sig);

    // purchase via meta-tx
    const buyData = pm.interface.encodeFunctionData("purchase", [1, referrer.address]);
    const req2 = { ...req, to: pm.address, data: buyData, nonce: (await forwarder.getNonce(user.address)).toNumber() };
    const sig2 = await signRequest(req2, user);
    await forwarder.connect(owner).execute(req2, sig2);

    expect(await shareToken.balanceOf(owner.address)).to.equal(0); // verify flow
  });

});
