const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("MinimalForwarder MetaTx", function () {
  let forwarder, token, recipient, sender, relayer;
  let domain, types;

  beforeEach(async () => {
    [sender, relayer, recipient] = await ethers.getSigners();

    const MinimalForwarder = await ethers.getContractFactory("MinimalForwarder");
    forwarder = await MinimalForwarder.deploy();
    await forwarder.deployed();

    const ERC20 = await ethers.getContractFactory("MyERC20");
    token = await ERC20.deploy("MetaToken", "MTK", sender.address, 1000);
    await token.deployed();

    domain = {
      name: "MinimalForwarder",
      version: "0.0.1",
      chainId: (await ethers.provider.getNetwork()).chainId,
      verifyingContract: forwarder.address,
    };

    types = {
      ForwardRequest: [
        { name: "from", type: "address" },
        { name: "to", type: "address" },
        { name: "value", type: "uint256" },
        { name: "gas", type: "uint256" },
        { name: "nonce", type: "uint256" },
        { name: "data", type: "bytes" },
      ],
    };
  });

  it("should relay ERC20 approve meta-tx correctly", async () => {
    const amount = 100;
    const nonce = await forwarder.getNonce(sender.address);
    const data = token.interface.encodeFunctionData("approve", [recipient.address, amount]);

    const request = {
      from: sender.address,
      to: token.address,
      value: 0,
      gas: 1e6,
      nonce: nonce.toString(),
      data: data,
    };

    const signature = await sender._signTypedData(domain, types, request);

    const tx = await forwarder.connect(relayer).execute(request, signature);
    await tx.wait();

    expect(await token.allowance(sender.address, recipient.address)).to.equal(amount);
  });
});