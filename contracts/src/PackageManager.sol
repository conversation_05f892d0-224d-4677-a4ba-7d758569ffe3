// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/metatx/ERC2771Context.sol";

interface IShareToken { function mint(address to, uint256 amount) external; }
interface ILPToken    { function mint(address to, uint256 amount) external; }
interface IVestingVault{ function lock(address user, uint256 amount) external; }
interface IReferralManager { function payReferral(address referrer, uint256 amount) external; }

contract PackageManager is ERC2771Context, AccessControl, ReentrancyGuard {
    using SafeERC20 for IERC20;
    bytes32 public constant ADMIN_ROLE = DEFAULT_ADMIN_ROLE;

    struct Package {
        uint256 entryUSDT;
        uint16  tokenPriceBps;
        uint16  vestSplitBps;
        uint16  referralRateBps;
        bool    exists;
    }

    IERC20           public immutable usdt;
    IShareToken      public immutable shareToken;
    ILPToken         public immutable lpToken;
    IVestingVault    public immutable vestingVault;
    IReferralManager public immutable referralManager;
    address          public immutable liquidityWallet;
    address          public immutable usdtVault;

    mapping(uint256 => Package) private _packages;
    uint256[]                   private _packageIds;

    event PackageAdded(uint256 indexed id, uint256 entryUSDT, uint16 priceBps, uint16 vestBps, uint16 referralBps);
    event PackageUpdated(uint256 indexed id);
    event PackageRemoved(uint256 indexed id);
    event Purchased(address indexed buyer, uint256 indexed packageId, uint256 usdtAmount, address indexed referrer);
    event ReferralPaid(address indexed referrer, address indexed buyer, uint256 reward);

    constructor(
        address forwarder,
        address usdt_,
        address shareToken_,
        address lpToken_,
        address vestingVault_,
        address referralManager_,
        address liquidityWallet_,
        address usdtVault_,
        address admin
    ) ERC2771Context(forwarder) {
        require(usdt_           != address(0), "USDT zero");
        require(shareToken_     != address(0), "ShareToken zero");
        require(lpToken_        != address(0), "LPToken zero");
        require(vestingVault_   != address(0), "VestingVault zero");
        require(referralManager_!= address(0), "ReferralMgr zero");
        require(liquidityWallet_!= address(0), "Liquidity zero");
        require(usdtVault_      != address(0), "USDTVault zero");
        require(admin           != address(0), "Admin zero");

        usdt            = IERC20(usdt_);
        shareToken      = IShareToken(shareToken_);
        lpToken         = ILPToken(lpToken_);
        vestingVault    = IVestingVault(vestingVault_);
        referralManager = IReferralManager(referralManager_);
        liquidityWallet = liquidityWallet_;
        usdtVault       = usdtVault_;

        _grantRole(ADMIN_ROLE, admin);
    }

    function _msgSender() internal view override(Context, ERC2771Context) returns (address) {
        return ERC2771Context._msgSender();
    }
    function _msgData() internal view override(Context, ERC2771Context) returns (bytes calldata) {
        return ERC2771Context._msgData();
    }
    function _contextSuffixLength() internal view override(Context, ERC2771Context) returns (uint256) {
        return ERC2771Context._contextSuffixLength();
    }

    function addPackage(
        uint256 id,
        uint256 entryUSDT,
        uint16  priceBps,
        uint16  vestBps,
        uint16  referralBps
    ) external onlyRole(ADMIN_ROLE) {
        require(id != 0,                 "Id zero");
        require(entryUSDT > 0,           "Entry zero");
        require(!_packages[id].exists,   "Exists");
        require(priceBps > 0,            "Price zero");
        require(vestBps <= 10000,        "Vest too high");
        require(referralBps <= 10000,    "Referral too high");

        _packages[id] = Package(entryUSDT, priceBps, vestBps, referralBps, true);
        _packageIds.push(id);
        emit PackageAdded(id, entryUSDT, priceBps, vestBps, referralBps);
    }

    function updatePackage(
        uint256 id,
        uint256 entryUSDT,
        uint16  priceBps,
        uint16  vestBps,
        uint16  referralBps
    ) external onlyRole(ADMIN_ROLE) {
        require(_packages[id].exists, "Not found");
        require(priceBps > 0,          "Price zero");
        require(vestBps <= 10000,      "Vest too high");
        require(referralBps <= 10000,  "Referral too high");

        Package storage pkg = _packages[id];
        pkg.entryUSDT       = entryUSDT;
        pkg.tokenPriceBps   = priceBps;
        pkg.vestSplitBps    = vestBps;
        pkg.referralRateBps = referralBps;
        emit PackageUpdated(id);
    }

    function removePackage(uint256 id) external onlyRole(ADMIN_ROLE) {
        require(_packages[id].exists, "Not found");
        _packages[id].exists = false;
        for (uint i; i < _packageIds.length; i++) {
            if (_packageIds[i] == id) {
                _packageIds[i] = _packageIds[_packageIds.length - 1];
                _packageIds.pop();
                break;
            }
        }
        emit PackageRemoved(id);
    }

    function getPackageIds() external view returns (uint256[] memory) {
        return _packageIds;
    }

    function getPackage(uint256 id) external view returns (Package memory) {
        require(_packages[id].exists, "Not found");
        return _packages[id];
    }

    function purchase(uint256 id, address referrer) external nonReentrant {
        address buyer = _msgSender();
        Package memory pkg = _packages[id];
        require(pkg.exists,         "Invalid pkg");
        require(pkg.entryUSDT > 0,  "Zero amount");

        usdt.safeTransferFrom(buyer, address(this), pkg.entryUSDT);

        uint256 usdtVest   = pkg.entryUSDT * pkg.vestSplitBps   / 10000;
        uint256 usdtLiq    = pkg.entryUSDT - usdtVest;
        usdt.safeTransfer(usdtVault,       usdtVest);
        usdt.safeTransfer(liquidityWallet, usdtLiq);

        uint256 totalTokens = pkg.entryUSDT * 10000 / pkg.tokenPriceBps;
        uint256 vestTokens  = totalTokens  * pkg.vestSplitBps  / 10000;
        uint256 liqTokens   = totalTokens - vestTokens;

        shareToken.mint(address(vestingVault), vestTokens);
        shareToken.mint(liquidityWallet,       liqTokens);

        lpToken.mint(buyer, totalTokens);
        vestingVault.lock(buyer, vestTokens);

        if (referrer != address(0) && pkg.referralRateBps > 0) {
            uint256 reward = totalTokens * pkg.referralRateBps / 10000;
            referralManager.payReferral(referrer, reward);
            emit ReferralPaid(referrer, buyer, reward);
        }

        emit Purchased(buyer, id, pkg.entryUSDT, referrer);
    }
}
