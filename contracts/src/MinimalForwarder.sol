// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";

/// @title MinimalForwarder — a slim EIP-2771 relayer / forwarder contract
/// @notice Accepts signed meta-transactions, validates them on-chain, increments nonces,
///         and forwards the call (with appended original `from`) to the target.
///
///  Usage:
///       • Takes user-signed `ForwardRequest` + `signature`
///       • Submits `execute(req, sig)` with enough gas to cover `req.gas`
///       • Pays gas in BNB (optionally via  Paymaster)
contract MinimalForwarder {
    using ECDSA for bytes32;

    struct ForwardRequest {
        address from;
        address to;
        uint256 value;
        uint256 gas;
        uint256 nonce;
        bytes   data;
    }

    // keccak256("ForwardRequest(address from,address to,uint256 value,uint256 gas,uint256 nonce,bytes data)")
    bytes32 private constant _TYPEHASH =
        0x8fcbaf0c3b8bf4c8d14f79065f4d5e7c9d02df71830c6e2a4b7c8d2296d6e496;

    // domain separator: keccak256(EIP712Domain(string name,string version,uint256 chainId,address verifyingContract))
    bytes32 private immutable _DOMAIN_SEPARATOR;

    mapping(address => uint256) private _nonces;

    constructor() {
        _DOMAIN_SEPARATOR = _buildDomainSeparator();
    }

    /// @notice Returns the current nonce for `from`. Must be included in the signed payload.
    function getNonce(address from) external view returns (uint256) {
        return _nonces[from];
    }

    /// @notice Verifies the signature is valid and nonce matches.
    function verify(ForwardRequest calldata req, bytes calldata signature) public view returns (bool) {
        bytes32 hashStruct = keccak256(
            abi.encode(
                _TYPEHASH,
                req.from,
                req.to,
                req.value,
                req.gas,
                req.nonce,
                keccak256(req.data)
            )
        );
        bytes32 digest = keccak256(abi.encodePacked("\x19\x01", _DOMAIN_SEPARATOR, hashStruct));
        return _nonces[req.from] == req.nonce && digest.recover(signature) == req.from;
    }

    /// @notice Relays the call if `verify(...)` passes. Increments nonce and forwards `data` + `from` to `to`.
    function execute(ForwardRequest calldata req, bytes calldata signature)
        external
        payable
        returns (bool, bytes memory)
    {
        require(verify(req, signature), "MinimalForwarder: signature mismatch");
        _nonces[req.from]++;

        // Append the `from` address to the calldata for ERC2771Context to extract.
        bytes memory callData = abi.encodePacked(req.data, req.from);

        (bool success, bytes memory returndata) = req.to.call{gas: req.gas, value: req.value}(callData);
        // Ensure the callee didn’t consume all gas (avoiding griefing).
        assert(gasleft() > req.gas / 63);

        return (success, returndata);
    }

    /// @dev Builds EIP-712 domain separator from contract parameters.
    function _buildDomainSeparator() private view returns (bytes32) {
        return keccak256(
            abi.encode(
                keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"),
                keccak256(bytes("MinimalForwarder")),
                keccak256(bytes("0.0.1")),
                block.chainid,
                address(this)
            )
        );
    }
}
