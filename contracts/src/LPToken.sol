// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";

/// @title LPToken — 1:1 USDT-backed LP token with role-based minting and burning
/// @notice Minted on share purchase; burned on vesting claim
contract LPToken is ERC20, ERC20Burnable, AccessControl {
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant BURNER_ROLE = keccak256("BURNER_ROLE");

    /// @param name_   Token name, e.g. "BlockCoop LP Token"
    /// @param symbol_ Token symbol, e.g. "BCLP"
    /// @param admin   Address that receives DEFAULT_ADMIN_ROLE
    /// @param minter  Address granted MINTER_ROLE (e.g. PackageManager)
    /// @param burner  Address granted BURNER_ROLE (e.g. V<PERSON><PERSON>Vault)
    constructor(
        string memory name_,
        string memory symbol_,
        address admin,
        address minter,
        address burner
    ) ERC20(name_, symbol_) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(MINTER_ROLE, minter);
        _grantRole(BURNER_ROLE, burner);
    }

    /// @notice Mint LP tokens to `to`. Only MINTER_ROLE.
    function mint(address to, uint256 amount) external onlyRole(MINTER_ROLE) {
        _mint(to, amount);
    }

    /// @notice Burn tokens from `account`. Only BURNER_ROLE.
    /// @dev Overrides ERC20Burnable's burnFrom to restrict role.
    function burnFrom(address account, uint256 amount)
        public
        override
        onlyRole(BURNER_ROLE)
    {
        _burn(account, amount);
    }
}
