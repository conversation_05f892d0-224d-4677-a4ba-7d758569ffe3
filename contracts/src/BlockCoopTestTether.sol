// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/// @title BlockCoopTestTether — 6‑decimal ERC20 clone of USDT for testnets
/// @notice Mints an initial supply to the owner, and owner can mint unlimited more
contract BlockCoopTestTether is ERC20, Ownable {
    /// @notice URI pointing to token logo (for frontend wallets)
    string public logoURI;

    constructor() ERC20("BlockCoop Test Tether", "USDT") {
        logoURI = "https://amaranth-accused-buzzard-564.mypinata.cloud/ipfs/bafkreibn4y6llleughtp5pgu37lve7mymvcffpo5i2h6iw4t4iwo6z5ocu";

        // Initial mint: 100,000,000,000 tokens (6 decimals)
        // 100 billion × 10⁶ = 100_000_000_000_000_000
        _mint(msg.sender, 100_000_000_000 * 10**decimals());
    }

    /// @notice Override decimals to match USDT’s 6‑place precision
    function decimals() public pure override returns (uint8) {
        return 6;
    }

    /// @notice Owner can mint arbitrary additional tokens
    /// @param to     recipient address
    /// @param amount token amount (including decimals)
    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }
}
