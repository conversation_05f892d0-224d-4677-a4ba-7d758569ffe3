// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";

/// @title ShareToken — mintable, burnable ERC‑20 with multisig‑governed roles and configurable AMM tax buckets
contract ShareToken is ERC20, ERC20Burnable, AccessControl {
    using EnumerableSet for EnumerableSet.AddressSet;

    /// Roles
    bytes32 public constant MULTISIG_ROLE = keccak256("MULTISIG_ROLE");
    bytes32 public constant TAX_MANAGER_ROLE = keccak256("TAX_MANAGER_ROLE");

    /// AMM pairs for tax application
    EnumerableSet.AddressSet private _ammPairs;

    /// Tax bucket info
    struct TaxBucket { uint16 rateBps; address wallet; }
    mapping(bytes32 => TaxBucket) public buckets;
    bytes32[] public bucketKeys;
    uint16 public totalFeeBps;

    /// Events
    event AMMPairUpdated(address indexed pair, bool enabled);
    event TaxBucketUpdated(bytes32 indexed key, uint16 rateBps, address wallet);
    event FeesDistributed(address indexed from, uint256 amount);

    constructor(
        string memory name_,
        string memory symbol_,
        address multisig
    ) ERC20(name_, symbol_) {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(MULTISIG_ROLE, multisig);
        _grantRole(TAX_MANAGER_ROLE, multisig);
    }

    /**
     * @notice Mint new tokens. Only MULTISIG_ROLE can call.
     */
    function mint(address to, uint256 amount)
        external
        onlyRole(MULTISIG_ROLE)
    {
        _mint(to, amount);
    }

    /**
     * @notice Burn tokens from any account. Only MULTISIG_ROLE can call.
     */
    function burnFrom(address account, uint256 amount)
        public
        override
        onlyRole(MULTISIG_ROLE)
    {
        _burn(account, amount);
    }

    /**
     * @notice Add or remove an AMM pair for fee logic.
     */
    function setAMMPair(address pair, bool enabled)
        external
        onlyRole(TAX_MANAGER_ROLE)
    {
        if (enabled) {
            _ammPairs.add(pair);
        } else {
            _ammPairs.remove(pair);
        }
        emit AMMPairUpdated(pair, enabled);
    }

    /**-
     * @notice Configure a tax bucket.
     * @param key Unique identifier (e.g. "LIQUIDITY", "STAKING").
     * @param rateBps Rate in basis points (1 bps = 0.01%).
     * @param wallet Destination wallet for collected fees.
     */
    function setTaxBucket(
        bytes32 key,
        uint16 rateBps,
        address wallet
    ) external onlyRole(TAX_MANAGER_ROLE) {
        require(totalFeeBps - buckets[key].rateBps + rateBps <= 1000, "Total fee too high");
        if (buckets[key].wallet == address(0)) {
            bucketKeys.push(key);
        }
        totalFeeBps = totalFeeBps - buckets[key].rateBps + rateBps;
        buckets[key] = TaxBucket({ rateBps: rateBps, wallet: wallet });
        emit TaxBucketUpdated(key, rateBps, wallet);
    }

    /**
     * @dev Override transfer to apply taxes when interacting with AMM pairs.
     */
    function _transfer(
        address sender,
        address recipient,
        uint256 amount
    ) internal override {
        if (_ammPairs.contains(sender) || _ammPairs.contains(recipient)) {
            uint256 fee = (amount * totalFeeBps) / 10000;
            if (fee > 0) {
                // Distribute fees
                for (uint i = 0; i < bucketKeys.length; i++) {
                    TaxBucket memory b = buckets[bucketKeys[i]];
                    uint256 share = (amount * b.rateBps) / 10000;
                    super._transfer(sender, b.wallet, share);
                }
                amount -= fee;
                emit FeesDistributed(sender, fee);
            }
        }
        super._transfer(sender, recipient, amount);
    }

    /**
     * @notice Expose set of registered AMM pairs.
     */
    function listAMMPairs() external view returns (address[] memory) {
        return _ammPairs.values();
    }

    /**
     * @notice Expose bucket keys for enumeration.
     */
    function listTaxBuckets() external view returns (bytes32[] memory) {
        return bucketKeys;
    }
}
