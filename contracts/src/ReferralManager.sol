// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";

/// @title ReferralManager — holds ShareTokens for referral rewards and distributes them
/// @notice PackageManager calls `payReferral` to reward referrers; admin can withdraw or top up as needed
contract ReferralManager is AccessControl {
    using SafeERC20 for IERC20;

    bytes32 public constant ADMIN_ROLE   = DEFAULT_ADMIN_ROLE;
    bytes32 public constant CALLER_ROLE  = keccak256("CALLER_ROLE");

    /// @notice ShareToken (ERC20) from which referrals are paid
    IERC20 public immutable shareToken;

    event ReferralPaid(address indexed referrer, address indexed buyer, uint256 amount);
    event TokensWithdrawn(address indexed to, uint256 amount);

    /// @param shareToken_ Address of the ShareToken contract
    /// @param caller      Address permitted to call `payReferral` (e.g., PackageManager)
    /// @param admin       Address with ADMIN_ROLE
    constructor(address shareToken_, address caller, address admin) {
        shareToken = IERC20(shareToken_);
        _grantRole(CALLER_ROLE, caller);
        _grantRole(ADMIN_ROLE, admin);
    }

    /// @notice Pay referral reward to `referrer`
    /// @dev Only accounts with CALLER_ROLE (e.g., PackageManager) can call
    /// @param referrer Address receiving the referral reward
    /// @param amount   Amount of ShareTokens to transfer
    function payReferral(address referrer, uint256 amount) external onlyRole(CALLER_ROLE) {
        require(referrer != address(0), "Invalid referrer");
        require(amount > 0, "Amount must be > 0");
        shareToken.safeTransfer(referrer, amount);
        emit ReferralPaid(referrer, msg.sender, amount);
    }

    /// @notice Admin can withdraw leftover ShareTokens to a specified address
    /// @param to     Recipient address
    /// @param amount Number of tokens to withdraw
    function withdrawTokens(address to, uint256 amount) external onlyRole(ADMIN_ROLE) {
        require(to != address(0), "Invalid recipient");
        require(amount > 0, "Amount must be > 0");
        shareToken.safeTransfer(to, amount);
        emit TokensWithdrawn(to, amount);
    }
}
