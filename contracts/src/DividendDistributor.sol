// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/utils/Context.sol";
import "@openzeppelin/contracts/metatx/ERC2771Context.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";

/// @title DividendDistributor — pro-rata USDT (or any ERC20) dividends to ShareToken holders, with meta-tx support
contract DividendDistributor is ERC2771Context, Ownable {
    using SafeERC20 for IERC20;
    using EnumerableSet for EnumerableSet.AddressSet;

    IERC20 public immutable rewardToken;  
    IERC20 public immutable shareToken;   // Share holders reward Token 

    uint256 private constant ACC_FACTOR = 1e36;  

    uint256 public dividendsPerShare;    // cumulative rewards per share, scaled by ACC_FACTOR
    uint256 public totalDistributed;     // total rewards ever deposited

    EnumerableSet.AddressSet private _shareholders;
    mapping(address => uint256) private _sharesExcluded; // “cumulative” at last deposit/claim
    mapping(address => uint256) public claimed;          // total claimed per account

    event Deposited(address indexed from, uint256 amount, uint256 newDividendsPerShare);
    event Claimed(address indexed to, uint256 amount);

    /// @param forwarder     Trusted minimal forwarder for meta-tx
    /// @param _shareToken   ShareToken address
    /// @param _rewardToken  Reward token address i.e USDT
    constructor(
        address forwarder,
        address _shareToken,
        address _rewardToken
    ) ERC2771Context(forwarder) Ownable() {
        require(_shareToken != address(0), "DD: zero share token");
        require(_rewardToken != address(0), "DD: zero reward token");

        shareToken  = IERC20(_shareToken);
        rewardToken = IERC20(_rewardToken);
    }

    /// @inheritdoc ERC2771Context
    function _msgSender() internal view override(Context, ERC2771Context) returns (address) {
        return ERC2771Context._msgSender();
    }

    /// @inheritdoc ERC2771Context
    function _msgData() internal view override(Context, ERC2771Context) returns (bytes calldata) {
        return ERC2771Context._msgData();
    }

    /// @inheritdoc ERC2771Context
    function _contextSuffixLength() internal view override(Context, ERC2771Context) returns (uint256) {
        return ERC2771Context._contextSuffixLength();
    }

    /// @notice Deposit reward tokens for distribution. Caller must `approve` this contract first.
    /// @param amount The amount of reward tokens to deposit.
    function deposit(uint256 amount) external {
        address sender = _msgSender();
        require(amount > 0, "DD: zero amount");

        uint256 totalShares = shareToken.totalSupply();
        require(totalShares > 0, "DD: no shares exist");

        // pull in the tokens
        rewardToken.safeTransferFrom(sender, address(this), amount);

        // bump dividendsPerShare
        dividendsPerShare += (amount * ACC_FACTOR) / totalShares;
        totalDistributed += amount;

        emit Deposited(sender, amount, dividendsPerShare);
    }

    /// @notice View unpaid dividends for an account.
    function unpaidEarnings(address account) public view returns (uint256) {
        uint256 balance = shareToken.balanceOf(account);
        uint256 cumulative = (balance * dividendsPerShare) / ACC_FACTOR;
        uint256 excluded   = _sharesExcluded[account];
        return cumulative > excluded ? cumulative - excluded : 0;
    }

    /// @notice Claim your pending dividends.
    function claim() external {
        address user = _msgSender();
        _trackShareholder(user);

        uint256 amount = unpaidEarnings(user);
        require(amount > 0, "DD: no dividends");

        // update exclusion so future `unpaidEarnings` starts fresh
        _sharesExcluded[user] = (shareToken.balanceOf(user) * dividendsPerShare) / ACC_FACTOR;
        claimed[user] += amount;

        rewardToken.safeTransfer(user, amount);
        emit Claimed(user, amount);
    }

    /// @dev Track a shareholder for future distributions.
    ///      On first interaction we set their excluded cumulative to current.
    function _trackShareholder(address account) internal {
        if (!_shareholders.contains(account)) {
            _shareholders.add(account);
            _sharesExcluded[account] = (shareToken.balanceOf(account) * dividendsPerShare) / ACC_FACTOR;
        }
    }

    /// @notice Get the full list of tracked shareholders.
    function shareholders() external view returns (address[] memory) {
        return _shareholders.values();
    }

    /// @notice Rescue any ERC20 sent here by mistake. Only owner.
    function rescueTokens(address token, address to, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(to, amount);
    }
}
