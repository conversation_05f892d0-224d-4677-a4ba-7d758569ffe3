// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/utils/Context.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/metatx/ERC2771Context.sol";

/// @notice Interface to mint ShareTokens
interface IShareToken {
    function mint(address to, uint256 amount) external;
}

/// @notice Interface to mint and burn LP Tokens
interface ILPToken {
    function mint(address to, uint256 amount) external;
    function burnFrom(address account, uint256 amount) external;
}

/// @title VestingVault — locks ShareTokens and enables users to claim vested tokens by burning LP tokens (meta-tx enabled)
contract VestingVault is ERC2771Context, AccessControl, ReentrancyGuard {
    using SafeERC20 for IERC20;

    bytes32 public constant ADMIN_ROLE  = DEFAULT_ADMIN_ROLE;
    bytes32 public constant LOCKER_ROLE = keccak256("LOCKER_ROLE");

    struct Lock {
        uint256 amount;
        uint256 start;
        uint32  cliff;
        uint32  duration;
    }

    IShareToken public immutable shareToken;
    ILPToken    public immutable lpToken;
    uint32      public defaultCliff;
    uint32      public defaultDuration;

    mapping(address => Lock[]) private _locks;
    mapping(address => uint256)   public claimed;

    event Locked(address indexed user, uint256 amount, uint256 start);
    event Claimed(address indexed user, uint256 amount);
    event DefaultParamsUpdated(uint32 cliff, uint32 duration);

    constructor(
        address forwarder,
        address shareToken_,
        address lpToken_,
        address locker,
        uint32  cliff_,
        uint32  duration_,
        address admin
    ) ERC2771Context(forwarder) {
        require(shareToken_    != address(0), "shareToken zero");
        require(lpToken_       != address(0), "lpToken zero");
        require(locker         != address(0), "locker zero");
        require(admin          != address(0), "admin zero");

        shareToken     = IShareToken(shareToken_);
        lpToken        = ILPToken(lpToken_);
        defaultCliff   = cliff_;
        defaultDuration= duration_;

        _grantRole(LOCKER_ROLE, locker);
        _grantRole(ADMIN_ROLE,  admin);
    }

    // Only these two overrides are needed:
    function _msgSender()
        internal
        view
        override(Context, ERC2771Context)
        returns (address)
    {
        return ERC2771Context._msgSender();
    }

    function _msgData()
        internal
        view
        override(Context, ERC2771Context)
        returns (bytes calldata)
    {
        return ERC2771Context._msgData();
    }

    function _contextSuffixLength()
        internal
        view
        override(Context, ERC2771Context)
        returns (uint256)
    {
        return ERC2771Context._contextSuffixLength();
    }


    /// @notice Lock tokens for vesting (by PackageManager or other locker)
    function lock(address user, uint256 amount)
        external
        onlyRole(LOCKER_ROLE)
    {
        require(amount > 0, "Cannot lock zero");
        _locks[user].push(Lock({
            amount:   amount,
            start:    block.timestamp,
            cliff:    defaultCliff,
            duration: defaultDuration
        }));
        emit Locked(user, amount, block.timestamp);
    }

    /// @notice View vested tokens for `user`
    function vestedOf(address user) public view returns (uint256 total) {
        Lock[] storage arr = _locks[user];
        uint256 nowTs = block.timestamp;
        for (uint i; i < arr.length; i++) {
            Lock storage l = arr[i];
            if (nowTs < l.start + l.cliff) continue;
            uint256 elapsed = nowTs - (l.start + l.cliff);
            total += (elapsed >= l.duration ? l.amount : (l.amount * elapsed) / l.duration);
        }
    }

    /// @notice Claimable = vested minus already claimed
    function claimable(address user) public view returns (uint256) {
        return vestedOf(user) - claimed[user];
    }

    /// @notice Claim vested ShareTokens by burning LP tokens
    function claim(uint256 amount) external nonReentrant {
        address sender = _msgSender();
        uint256 available = claimable(sender);
        require(available > 0, "Nothing to claim");
        require(amount == available, "Must burn entire claimable");

        lpToken.burnFrom(sender, amount);
        claimed[sender] += amount;
        IERC20(address(shareToken)).safeTransfer(sender, amount);
        emit Claimed(sender, amount);
    }

    /// @notice Update default cliff/duration
    function setDefaultParams(uint32 cliff_, uint32 duration_)
        external
        onlyRole(ADMIN_ROLE)
    {
        defaultCliff    = cliff_;
        defaultDuration = duration_;
        emit DefaultParamsUpdated(cliff_, duration_);
    }

    /// @notice Retrieve raw lock schedule for `user`
    function getLocks(address user) external view returns (Lock[] memory) {
        return _locks[user];
    }
}
