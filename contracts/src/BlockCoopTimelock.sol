// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/governance/TimelockController.sol";

/// @title BlockCoopTimelock — governance timelock for managing on-chain upgrades and settings
contract BlockCoopTimelock is TimelockController {
    constructor(
        uint256 minDelaySeconds,
        address[] memory proposers,
        address[] memory executors,
        address admin
    ) TimelockController(minDelaySeconds, proposers, executors, admin) {}
}
