{"_format": "hh-sol-cache-2", "files": {"/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/BlockCoopProxyAdmin.sol": {"lastModificationDate": 1748115088052, "contentHash": "edec3a3398962ea7f9a8295b2b83996b", "sourceName": "contracts/src/BlockCoopProxyAdmin.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/proxy/transparent/ProxyAdmin.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["BlockCoopProxyAdmin"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/proxy/transparent/ProxyAdmin.sol": {"lastModificationDate": 1748115221657, "contentHash": "9891986e27d357222a8ac8c0c13abe31", "sourceName": "@openzeppelin/contracts/proxy/transparent/ProxyAdmin.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./TransparentUpgradeableProxy.sol", "../../access/Ownable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ProxyAdmin"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1748115221656, "contentHash": "5a20b2cad87ddb61c7a3a6af21289e28", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Ownable"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"lastModificationDate": 1748115221665, "contentHash": "1286aa8d056c7120d1a2da252e310d2f", "sourceName": "@openzeppelin/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC1967/ERC1967Proxy.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ITransparentUpgradeableProxy", "TransparentUpgradeableProxy"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1748115221589, "contentHash": "f07feb4a44b1a4872370da5aa70e8e46", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Context"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"lastModificationDate": 1748115221612, "contentHash": "3fc3c7c0a2956f36e766691bb9473b06", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Proxy.sol", "./ERC1967Upgrade.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC1967Proxy"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/proxy/Proxy.sol": {"lastModificationDate": 1748115221657, "contentHash": "40b3d81a836d50ff47e03893dcaaf204", "sourceName": "@openzeppelin/contracts/proxy/Proxy.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Proxy"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Upgrade.sol": {"lastModificationDate": 1748115221612, "contentHash": "a127706394bead18392601a20d44867a", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Upgrade.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../beacon/IBeacon.sol", "../../interfaces/IERC1967.sol", "../../interfaces/draft-IERC1822.sol", "../../utils/Address.sol", "../../utils/StorageSlot.sol"], "versionPragmas": ["^0.8.2"], "artifacts": ["ERC1967Upgrade"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/Address.sol": {"lastModificationDate": 1748115221579, "contentHash": "211ffd288c1588ba8c10eae668ca3c66", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["Address"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol": {"lastModificationDate": 1748115221634, "contentHash": "d0d060231a45da7a1eecbb5cd286fa40", "sourceName": "@openzeppelin/contracts/interfaces/IERC1967.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1967"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol": {"lastModificationDate": 1748115221630, "contentHash": "b6bd23bf19e90b771337037706470933", "sourceName": "@openzeppelin/contracts/proxy/beacon/IBeacon.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IBeacon"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/interfaces/draft-IERC1822.sol": {"lastModificationDate": 1748115221604, "contentHash": "2858d98e74e67987ec81b39605230b74", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC1822.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1822Proxiable"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1748115221663, "contentHash": "682f7dd1f2e1147c8390e7575deceb2d", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["StorageSlot"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"lastModificationDate": 1748115221659, "contentHash": "1b5d667d3740d866eca0352758e59827", "sourceName": "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol", "../extensions/IERC20Permit.sol", "../../../utils/Address.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["SafeERC20"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1748115221634, "contentHash": "df36f7051335cd1e748b1b6463b7fdd3", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol": {"lastModificationDate": 1748115221634, "contentHash": "525fcdad8d171312933f47baf01d1ed8", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Permit"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1748115221634, "contentHash": "909ab67fc5c25033fe6cd364f8c056f9", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Metadata"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1748115221612, "contentHash": "********************************", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC20"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"lastModificationDate": 1748115221613, "contentHash": "a1c7f80ae26f5b2d7d563475627fbf25", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC20Burnable"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/access/AccessControl.sol": {"lastModificationDate": 1748115221575, "contentHash": "a2b1ec38a8dad325a596f926890772b8", "sourceName": "@openzeppelin/contracts/access/AccessControl.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IAccessControl.sol", "../utils/Context.sol", "../utils/Strings.sol", "../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["AccessControl"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1748115221664, "contentHash": "48686fc32a22a3754b8e63321857dd2a", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Strings"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol": {"lastModificationDate": 1748115221611, "contentHash": "0e7db055ce108f9da7bb6686a00287c0", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC165"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/access/IAccessControl.sol": {"lastModificationDate": 1748115221627, "contentHash": "57c84298234411cea19c7c284d86be8b", "sourceName": "@openzeppelin/contracts/access/IAccessControl.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IAccessControl"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1748115221663, "contentHash": "9488ebd4daacfee8ad04811600d7d061", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["SignedMath"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"lastModificationDate": 1748115221642, "contentHash": "fe63409d8a06818b926cf89e0ea88b1b", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Math"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"lastModificationDate": 1748115221633, "contentHash": "03e6768535ac4da0e9756f1d8a4a018a", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC165"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol": {"lastModificationDate": 1748115221632, "contentHash": "9f8822b72fe2702979e40160cb6d9636", "sourceName": "@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1155Receiver"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/governance/TimelockController.sol": {"lastModificationDate": 1748115221664, "contentHash": "39cfb1304186349ce1051fe20ff67567", "sourceName": "@openzeppelin/contracts/governance/TimelockController.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../access/AccessControl.sol", "../token/ERC721/IERC721Receiver.sol", "../token/ERC1155/IERC1155Receiver.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["TimelockController"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"lastModificationDate": 1748115221639, "contentHash": "c22d4395e33763de693fd440c6fd10e1", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721Receiver"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/BlockCoopTimelock.sol": {"lastModificationDate": 1748115088052, "contentHash": "c0105ed6c79cc2cc1d7b4ad03b61e481", "sourceName": "contracts/src/BlockCoopTimelock.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/governance/TimelockController.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["BlockCoopTimelock"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/VestingVault.sol": {"lastModificationDate": 1748196251520, "contentHash": "33f779943c3411d698683801b8e02913", "sourceName": "contracts/src/VestingVault.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/Context.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/security/ReentrancyGuard.sol", "@openzeppelin/contracts/metatx/ERC2771Context.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["ILPToken", "IShareToken", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/security/ReentrancyGuard.sol": {"lastModificationDate": 1748115221658, "contentHash": "1535f8c0c68463f8c1b5239f7584e71f", "sourceName": "@openzeppelin/contracts/security/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/PackageManager.sol": {"lastModificationDate": 1748194992352, "contentHash": "699bef9e3e44a4197eaabbd7944e0451", "sourceName": "contracts/src/PackageManager.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/security/ReentrancyGuard.sol", "@openzeppelin/contracts/metatx/ERC2771Context.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["ILPToken", "IReferralManager", "IShareToken", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PackageManager"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/ShareToken.sol": {"lastModificationDate": 1748115088053, "contentHash": "2c133a9e1458a96724b61f7b5b995fbb", "sourceName": "contracts/src/ShareToken.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/utils/structs/EnumerableSet.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["ShareToken"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/structs/EnumerableSet.sol": {"lastModificationDate": 1748115221605, "contentHash": "e029f029abc1fd2d85d54fd69086f076", "sourceName": "@openzeppelin/contracts/utils/structs/EnumerableSet.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["EnumerableSet"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/DividendDistributor.sol": {"lastModificationDate": 1748276418422, "contentHash": "285159080bd1ad9d772f2e990b792d65", "sourceName": "contracts/src/DividendDistributor.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/Context.sol", "@openzeppelin/contracts/metatx/ERC2771Context.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/structs/EnumerableSet.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["DividendDistributor"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/BlockCoopTestTether.sol": {"lastModificationDate": 1748115088052, "contentHash": "6cc127072c201302393354dacab3f77a", "sourceName": "contracts/src/BlockCoopTestTether.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["BlockCoopTestTether"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/ReferralManager.sol": {"lastModificationDate": 1748115088052, "contentHash": "35bdb2f1b19bd252402bb4f55c6d8efe", "sourceName": "contracts/src/ReferralManager.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/AccessControl.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["ReferralManager"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/LPToken.sol": {"lastModificationDate": 1748115088052, "contentHash": "b7ee91d03a00050816ccace7d6deeccf", "sourceName": "contracts/src/LPToken.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/access/AccessControl.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["LPToken"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1748115221604, "contentHash": "d822a8a9468649cab463f29f5decf5cc", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Strings.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ECDSA"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/contracts/src/MinimalForwarder.sol": {"lastModificationDate": 1748192800067, "contentHash": "02f2604dffe79d8455b7dd3a999145a8", "sourceName": "contracts/src/MinimalForwarder.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/cryptography/ECDSA.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["Minimal<PERSON><PERSON><PERSON><PERSON>"]}, "/home/<USER>/Documents/GKM/BlockCoop-Sacco/node_modules/@openzeppelin/contracts/metatx/ERC2771Context.sol": {"lastModificationDate": 1748115221617, "contentHash": "0eb1107c2f4b4236b40093bc815859e1", "sourceName": "@openzeppelin/contracts/metatx/ERC2771Context.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.9"], "artifacts": ["ERC2771Context"]}}}