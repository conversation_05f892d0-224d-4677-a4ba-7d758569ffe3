// jest.setup.js
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, '.env.test') });

// Set up test environment variables
process.env = {
  ...process.env,
  NODE_ENV: 'test',

  // API URLs
  BACKEND_URL: 'https://api.example.com',
  FRONTEND_URL: 'https://app.example.com',

  // M-Pesa (Daraja) API
  MPESA_CONSUMER_KEY: 'test-mpesa-consumer-key',
  MPESA_CONSUMER_SECRET: 'test-mpesa-consumer-secret',
  MPESA_PASSKEY: 'test-mpesa-passkey',
  MPESA_SHORTCODE: '174379',
  MPESA_CALLBACK_URL: 'https://api.example.com/api/payments/mpesa/callback',

  // Pesapal API
  PESAPAL_CONSUMER_KEY: 'test-pesapal-consumer-key',
  PESAPAL_CONSUMER_SECRET: 'test-pesapal-consumer-secret',
  PESAPAL_BASE_URL: 'https://pay.pesapal.com/v3',
  PESAPAL_IPN_ID: 'test-ipn-id',

  // Crypto settings
  DEPOSIT_ADDRESS: '******************************************',

  // Database connection
  DATABASE_URL: 'postgresql://test:test@localhost:5432/test_db',

  // JWT Secret
  JWT_SECRET: 'test-jwt-secret',

  // Web3 settings
  WEB3_PROVIDER_URL: 'https://data-seed-prebsc-1-s1.binance.org:8545',
  CONTRACT_ADDRESS: '******************************************',
  WALLET_PRIVATE_KEY: '******************************************000000000000000000000000'
};

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  // Keep error logging for debugging
  error: jest.fn(),
  // Uncomment to silence logs during tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
};

// Global setup
beforeAll(() => {
  // Any global setup before all tests
});

// Global teardown
afterAll(() => {
  // Any global cleanup after all tests
});
