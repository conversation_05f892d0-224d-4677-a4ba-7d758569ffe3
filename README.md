# BlockCoop Sacco

A blockchain-based cooperative savings and credit society (SACCO) platform built on BSC (Binance Smart Chain).

## Overview

BlockCoop is a decentralized financial platform that enables members to save, borrow, and invest using blockchain technology. The platform provides transparency, security, and efficiency in managing cooperative finances.

## Features

- Blockchain-based member registration and management
- Secure savings accounts with transparent transaction history
- Smart contract-based loan processing
- Dividend distribution system
- Governance voting mechanism

## Technology Stack

- Frontend: React.js
- Backend: Node.js
- Blockchain: BSC (Binance Smart Chain)
- Smart Contracts: Solidity
- Web3 Integration: viem

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn
- MetaMask or another Web3 wallet

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Bratipah/BlockCoop-Sacco.git
   cd BlockCoop-Sacco
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up environment variables:

   a. For the backend and blockchain services:
      - Copy the `.env.example` file in the root directory to `.env`:
        ```
        cp .env.example .env
        ```
      - Edit the `.env` file and replace the placeholder values with your actual configuration:
        ```
        # Required: Your BSC Testnet RPC URL (default should work)
        BSC_RPC=https://bsc-testnet.drpc.org

        # Required: BSC Testnet Chain ID (default should work)
        BSC_CHAIN_ID=97

        # Required for transactions: Your private key without 0x prefix
        PRIVATE_KEY=your_private_key_here

        # Optional: Set to 'production' for production environment
        NODE_ENV=development
        ```

   b. For the frontend:
      - Copy the `.env.example` file in the frontend/src directory to `.env.local`:
        ```
        cp frontend/src/.env.example frontend/src/.env.local
        ```
      - Edit the `.env.local` file as needed for your frontend configuration

4. Verify blockchain connectivity:
   ```
   npx ts-node -r dotenv/config contracts/scripts/check-connection.ts
   ```
   You should see output similar to:
   ```
   Block: 51428259n
   Network: 97
   ```
   This confirms your connection to the BSC Testnet.

5. Run the development server:
   ```
   npm start
   ```

## Testing

Run tests with:

```
npm test
```

For coverage reports:

```
npm test -- --coverage
```

## Environment Security

⚠️ **IMPORTANT: Never commit your `.env` files to the repository** ⚠️

The `.env` files contain sensitive information such as private keys and API credentials. These files are included in `.gitignore` to prevent them from being accidentally committed.

Best practices for environment security:

1. Always use `.env.example` files as templates without real credentials
2. Never share your private keys or API credentials
3. Use different private keys for development, testing, and production
4. Consider using a secrets manager for production environments
5. Regularly rotate your credentials

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For any inquiries, please open an issue on GitHub.