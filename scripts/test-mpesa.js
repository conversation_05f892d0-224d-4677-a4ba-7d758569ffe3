// scripts/test-mpesa.js
const axios = require('axios');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TEST_PHONE = '254708374149'; // Safaricom test phone number for successful payments

/**
 * Initiates an M-Pesa payment
 */
async function initiatePayment() {
  try {
    console.log('Initiating M-Pesa payment...');
    
    const response = await axios.post(`${API_URL}/api/payments/initiate`, {
      userAddress: '0x123456789abcdef',
      packageId: 'basic',
      paymentMethod: 'MPESA',
      amountKES: 1, // Use a small amount for testing
      phoneNumber: TEST_PHONE
    });
    
    console.log('\nPayment initiated successfully:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Error initiating payment:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Checks the status of a payment
 */
async function checkPaymentStatus(paymentId) {
  try {
    console.log(`\nChecking payment status for ${paymentId}...`);
    
    const response = await axios.get(`${API_URL}/api/payments/status/${paymentId}`);
    
    console.log('Payment status:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Error checking payment status:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Runs the payment jobs to check pending payments
 */
async function runPaymentJobs() {
  try {
    console.log('\nRunning payment jobs to check pending payments...');
    
    // This assumes you've exposed an API endpoint to run the jobs
    // If not, you can run the jobs using the npm script instead
    const response = await axios.post(`${API_URL}/api/admin/run-jobs`, {
      jobType: 'payments'
    });
    
    console.log('Jobs completed:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Error running payment jobs:', error.response?.data || error.message);
    console.log('You can run the jobs manually using: npm run run-payment-jobs');
  }
}

/**
 * Main test flow
 */
async function runTest() {
  try {
    // Step 1: Initiate payment
    const paymentData = await initiatePayment();
    const { paymentId } = paymentData;
    
    // Step 2: Wait for user to confirm they've received the STK push
    await new Promise(resolve => {
      rl.question('\nHave you received the STK push on your phone? (yes/no): ', answer => {
        console.log(`You answered: ${answer}`);
        resolve();
      });
    });
    
    // Step 3: Check payment status
    await checkPaymentStatus(paymentId);
    
    // Step 4: Wait for a while to allow the callback to be processed
    console.log('\nWaiting 30 seconds for the callback to be processed...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Step 5: Check payment status again
    await checkPaymentStatus(paymentId);
    
    // Step 6: Run payment jobs to check pending payments
    await runPaymentJobs();
    
    // Step 7: Check payment status one more time
    await checkPaymentStatus(paymentId);
    
    console.log('\nTest completed!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    rl.close();
  }
}

// Run the test
runTest();
