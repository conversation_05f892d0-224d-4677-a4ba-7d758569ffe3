// scripts/test-mpesa-api.js
require('dotenv').config();
const axios = require('axios');

// Environment variables
const {
  MPESA_CONSUMER_KEY,
  MPESA_CONSUMER_SECRET,
  MPESA_SHORTCODE,
  MPESA_PASSKEY,
  BACKEND_URL
} = process.env;

// Default sandbox shortcode if not provided
const shortcode = MPESA_SHORTCODE || '174379';

/**
 * Get OAuth token from Safaricom
 */
async function getOAuthToken() {
  try {
    console.log('Getting OAuth token...');
    
    const response = await axios.get(
      'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
      {
        auth: {
          username: MPESA_CONSUMER_KEY,
          password: MPESA_CONSUMER_SECRET
        },
        timeout: 10000,
        headers: {
          'Accept-Encoding': 'gzip, deflate, br'
        }
      }
    );
    
    console.log('OAuth token response:', response.data);
    return response.data.access_token;
  } catch (error) {
    console.error('Error getting OAuth token:', error.message);
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
    }
    throw error;
  }
}

/**
 * Send STK Push request
 */
async function sendStkPush(token, phoneNumber = '254708374149', amount = 1) {
  try {
    console.log(`Sending STK Push to ${phoneNumber} for KES ${amount}...`);
    
    // Generate timestamp and password
    const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
    const password = Buffer.from(`${shortcode}${MPESA_PASSKEY}${timestamp}`).toString('base64');
    
    // Format phone number
    let formattedPhone = phoneNumber;
    if (formattedPhone.startsWith('0')) {
      formattedPhone = `254${formattedPhone.substring(1)}`;
    } else if (!formattedPhone.startsWith('254')) {
      formattedPhone = `254${formattedPhone}`;
    }
    
    // Prepare payload
    const payload = {
      BusinessShortCode: shortcode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: Math.round(amount),
      PartyA: formattedPhone,
      PartyB: shortcode,
      PhoneNumber: formattedPhone,
      CallBackURL: `${BACKEND_URL || 'http://localhost:3000'}/api/payments/mpesa/callback`,
      AccountReference: 'Test',
      TransactionDesc: 'Test Payment'
    };
    
    console.log('STK Push payload:', JSON.stringify(payload, null, 2));
    
    const response = await axios.post(
      'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest',
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept-Encoding': 'gzip, deflate, br'
        },
        timeout: 15000
      }
    );
    
    console.log('STK Push response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending STK Push:', error.message);
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
    }
    throw error;
  }
}

/**
 * Check STK Push status
 */
async function checkStkStatus(token, checkoutRequestID) {
  try {
    console.log(`Checking STK Push status for ${checkoutRequestID}...`);
    
    // Generate timestamp and password
    const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
    const password = Buffer.from(`${shortcode}${MPESA_PASSKEY}${timestamp}`).toString('base64');
    
    const response = await axios.post(
      'https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query',
      {
        BusinessShortCode: shortcode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestID
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept-Encoding': 'gzip, deflate, br'
        },
        timeout: 15000
      }
    );
    
    console.log('STK Push status response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error checking STK Push status:', error.message);
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
    }
    throw error;
  }
}

/**
 * Run the test
 */
async function runTest() {
  try {
    // Get OAuth token
    const token = await getOAuthToken();
    
    // Send STK Push
    const stkResponse = await sendStkPush(token);
    
    // Wait for a while
    console.log('Waiting 10 seconds before checking status...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Check STK Push status
    if (stkResponse.CheckoutRequestID) {
      await checkStkStatus(token, stkResponse.CheckoutRequestID);
    }
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
runTest();
