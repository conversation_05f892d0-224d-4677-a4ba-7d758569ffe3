// scripts/deploy.ts
import hre from "hardhat";
import fs from "fs";
import path from "path";
import inquirer from "inquirer";

const { ethers } = hre;


function toCamelCase(name: string) {
  return name.charAt(0).toLowerCase() + name.slice(1);
}

async function main() {
  // 1) Load existing addresses (if any)
  const deployFile = path.resolve(__dirname, "../contracts/deployment.json");
  let addresses: Record<string,string> = {};
  if (fs.existsSync(deployFile)) {
    addresses = JSON.parse(fs.readFileSync(deployFile, "utf-8"));
  }

  // 2) List all .sol contracts in src/
  const srcDir = path.resolve(__dirname, "../contracts/src");
  const files = fs.readdirSync(srcDir)
    .filter((f) => f.endsWith(".sol"))
    .map((f) => f.replace(/\.sol$/, ""));

  // 3) Prompt user which to deploy
  const { toDeploy } = await inquirer.prompt({
    name:   "toDeploy",
    message:"Select contracts to deploy:",
    type:   "checkbox",
    choices: files,
  });
  if (toDeploy.length === 0) {
    console.log("Nothing selected. Exiting.");
    return;
  }

  const [deployer] = await ethers.getSigners();
  console.log("🚀 Deploying with:", deployer.address);

  // 4) Loop and deploy
  for (const name of toDeploy) {
    const factory = await ethers.getContractFactory(name);
    let args: any[] = [];

    switch (name) {
      case "MinimalForwarder":
        args = [];
        break;

      case "PackageManager":
        args = [
          addresses.minimalForwarder!, 
          addresses.usdtAddress!,
          addresses.shareToken!,
          addresses.lpToken!,
          addresses.vestingVault!,
          addresses.referralManager!,
          deployer.address,      // liquidityWallet
          deployer.address,      // usdtVault
          deployer.address,      // admin
        ];
        break;

      case "VestingVault":
        args = [
          addresses.minimalForwarder!,
          addresses.shareToken!,
          addresses.lpToken!,
          addresses.packageManager!,    // clerk
          5 * 365 * 24 * 3600,          // cliff
          365 * 24 * 3600,              // duration
          deployer.address,             // admin
        ];
        break;

      case "DividendDistributor":
        args = [
          addresses.minimalForwarder!,
          addresses.shareToken!,
          addresses.usdtAddress!
        ];
        break;

      // legacy / non-meta-tx contracts:
      case "ShareToken":
        args = ["BlockCoop Share Token", "BCST", deployer.address];
        break;
      case "LPToken":
        args = ["BlockCoop LP Token", "BCLP", deployer.address, deployer.address, deployer.address];
        break;
      case "BlockCoopTestTether":
        args = [];
        break;
      case "ReferralManager":
        args = [addresses.shareToken!, deployer.address, deployer.address];
        break;
      case "BlockCoopTimelock":
        args = [3600, [], [], deployer.address];
        break;
      case "PackageManager": /* already handled above */ break;
      default:
        console.warn(`⚠️  No args template for ${name}, deploying with none`);
    }

    // check for missing args
    for (const a of args) {
      if (a == null) {
        throw new Error(`Missing constructor arg for ${name}: ${JSON.stringify(args)}`);
      }
    }

    // deploy
    const contract = await factory.deploy(...args);
    await contract.waitForDeployment();
    const addr = await contract.getAddress();

    const key = toCamelCase(name);
    addresses[key] = addr;

    console.log(`✅ ${name} deployed to ${addr} as "${key}"`);
  }

  // 5) Save all addresses
  fs.writeFileSync(deployFile, JSON.stringify(addresses, null, 2));
  console.log("📄 Updated deployment.json");
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
