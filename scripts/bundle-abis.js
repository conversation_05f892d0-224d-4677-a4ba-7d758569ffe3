// scripts/bundle-abis.js
// Usage: node scripts/bundle-abis.js
// Bundles only non-debug, non-interface ABIs into frontend/src/contracts

const fs = require('fs');
const path = require('path');

const ROOT = path.resolve(__dirname, '..');
const FRONTEND_CONTRACTS = path.join(ROOT, 'frontend', 'src', 'contracts');
const DEPLOYMENT_JSON = path.join(ROOT, 'contracts', 'deployment.json');
const ARTIFACTS_SRC = path.join(ROOT, 'artifacts', 'contracts', 'src');

async function main() {
  // 1) Clean output directory
  fs.rmSync(FRONTEND_CONTRACTS, { recursive: true, force: true });
  fs.mkdirSync(FRONTEND_CONTRACTS, { recursive: true });

  // 2) Copy deployment.json
  fs.copyFileSync(DEPLOYMENT_JSON, path.join(FRONTEND_CONTRACTS, 'deployment.json'));

  // 3) Collect only non-.dbg.json and non-interface ABI files
  const abiFiles = new Set();
  for (const contractDir of fs.readdirSync(ARTIFACTS_SRC)) {
    const dirPath = path.join(ARTIFACTS_SRC, contractDir);
    if (!fs.statSync(dirPath).isDirectory()) continue;

    // List JSON files
    const files = fs.readdirSync(dirPath)
      .filter(f => f.endsWith('.json') && !f.endsWith('.dbg.json'))
      .filter(f => !/^I[A-Z].*\.json$/.test(f)); // exclude interface ABIs

    for (const file of files) {
      // Avoid duplicates
      if (abiFiles.has(file)) continue;
      const srcPath = path.join(dirPath, file);
      const destPath = path.join(FRONTEND_CONTRACTS, file);
      fs.copyFileSync(srcPath, destPath);
      abiFiles.add(file);
    }
  }

  // 4) Generate index.ts
  const lines = [];
  lines.push('// THIS FILE IS AUTO-GENERATED BY scripts/bundle-abis.js');
  lines.push('import addresses from "./deployment.json";');

  const sortedFiles = Array.from(abiFiles).sort();
  for (const file of sortedFiles) {
    const name = path.basename(file, '.json');
    lines.push(`import ${name}ABI from './${file}';`);
  }

  lines.push('\nexport const CONTRACT_ADDRESSES = addresses;\n');
  lines.push('export const ABIS = {');
  for (const file of sortedFiles) {
    const name = path.basename(file, '.json');
    lines.push(`  ${name}: ${name}ABI,`);
  }
  lines.push('};');

  fs.writeFileSync(
    path.join(FRONTEND_CONTRACTS, 'index.ts'),
    lines.join('\n')
  );

  console.log(`✅ Bundled ${sortedFiles.length} ABIs into ${FRONTEND_CONTRACTS}`);
}

main().catch(err => {
  console.error(err);
  process.exit(1);
});
