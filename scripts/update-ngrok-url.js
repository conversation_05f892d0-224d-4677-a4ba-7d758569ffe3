// scripts/update-ngrok-url.js
const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Ask for the ngrok URL
rl.question('Enter your ngrok URL (e.g., https://abc123def456.ngrok.io): ', (ngrokUrl) => {
  if (!ngrokUrl) {
    console.error('No URL provided. Exiting...');
    rl.close();
    return;
  }
  
  if (!ngrokUrl.startsWith('https://')) {
    console.error('URL must start with https://. Exiting...');
    rl.close();
    return;
  }
  
  // Read the .env file
  fs.readFile('.env', 'utf8', (err, data) => {
    if (err) {
      console.error('Error reading .env file:', err);
      rl.close();
      return;
    }
    
    // Update the BACKEND_URL and PESAPAL_IPN_URL
    const updatedData = data
      .replace(/BACKEND_URL=.*/g, `BACKEND_URL=${ngrokUrl}`)
      .replace(/PESAPAL_IPN_URL=.*/g, `PESAPAL_IPN_URL=${ngrokUrl}/api/payments/pesapal/ipn`);
    
    // Write the updated .env file
    fs.writeFile('.env', updatedData, 'utf8', (err) => {
      if (err) {
        console.error('Error writing .env file:', err);
        rl.close();
        return;
      }
      
      console.log('Successfully updated .env file with the following values:');
      console.log(`BACKEND_URL=${ngrokUrl}`);
      console.log(`PESAPAL_IPN_URL=${ngrokUrl}/api/payments/pesapal/ipn`);
      
      console.log('\nRestart your server for the changes to take effect.');
      rl.close();
    });
  });
});
