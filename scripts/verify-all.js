// scripts/verify-all.js

const hre   = require("hardhat");
const fs    = require("fs");
const path  = require("path");

// load previously saved addresses 
const deployment = require("../contracts/deployment.json");

async function verifyContract(address, constructorArgs, contractName) {
  console.log(`🔍 Verifying ${contractName} at ${address}...`);
  try {
    await hre.run("verify:verify", {
      address,
      constructorArguments: constructorArgs,
      contract: `contracts/src/${contractName}.sol:${contractName}`,
    });
    console.log(`✅ Verified ${contractName}`);
  } catch (e) {
    console.error(`❌ Failed to verify ${contractName}:`, e.message || e);
  }
}

async function main() {
  const inquirerModule = await import("inquirer");
  const prompt = inquirerModule.default?.prompt || inquirerModule.prompt;
  if (typeof prompt !== "function") {
    console.error("🚨 Inquirer prompt() not found!");
    process.exit(1);
  }

  // 1) find all contracts
  const srcDir = path.resolve(__dirname, "../contracts/src");
  const solFiles = fs
    .readdirSync(srcDir)
    .filter((f) => f.endsWith(".sol"))
    .map((f) => f.replace(/\.sol$/, ""));

  // 2) pick which to verify
  const { toVerify } = await prompt([{
    type:    "checkbox",
    name:    "toVerify",
    message: "Select contracts to verify:",
    choices: solFiles,
  }]);

  if (!toVerify.length) {
    console.log("No contracts selected. Exiting.");
    return;
  }

  // 3) address map
  const addressMap = {
    MinimalForwarder:     deployment.minimalForwarder,
    BlockCoopTestTether:  deployment.usdtAddress,
    ShareToken:           deployment.shareToken,
    LPToken:              deployment.lpToken,
    VestingVault:         deployment.vestingVault,
    ReferralManager:      deployment.referralManager,
    BlockCoopTimelock:    deployment.timelock,
    PackageManager:       deployment.packageManager,
    BlockCoopProxyAdmin:  deployment.proxyAdmin,
    DividendDistributor:  deployment.dividendDistributor
  };

  // 4) dispatch each
  for (const name of toVerify) {
    let args = [];

    switch (name) {
      case "MinimalForwarder":
        args = [];
        break;
      case "BlockCoopTestTether":
        args = [];
        break;
      case "ShareToken":
        args = ["BlockCoop Share Token", "BCST", deployment.admin];
        break;
      case "LPToken":
        args = ["BlockCoop LP Token", "BCLP", deployment.admin, deployment.admin, deployment.admin];
        break;
      case "VestingVault":
        args = [
          deployment.minimalForwarder,        
          deployment.shareToken,
          deployment.lpToken,
          deployment.packageManager,
          (5 * 365 * 24 * 3600).toString(),
          (365 * 24 * 3600).toString(),
          deployment.admin
        ];
        break;
      case "ReferralManager":
        args = [deployment.shareToken, deployment.admin, deployment.admin];
        break;
      case "BlockCoopTimelock":
        args = [3600, [], [], deployment.admin];
        break;
      case "PackageManager":
        args = [
          deployment.minimalForwarder,
          deployment.usdtAddress,
          deployment.shareToken,
          deployment.lpToken,
          deployment.vestingVault,
          deployment.referralManager,
          deployment.liquidityWallet,
          deployment.usdtVault,
          deployment.admin
        ];
        break;
      case "DividendDistributor":
        args = [deployment.minimalForwarder, deployment.shareToken, deployment.usdtAddress];
        break;
      case "BlockCoopProxyAdmin":
        args = [];
        break;
      default:
        console.log(`ℹ️  No predefined args for ${name}, using []`);
    }

    // sanity check
    if (args.some(a => a === undefined)) {
      console.error(`🚨 Missing args for ${name}, skipping.`);
      continue;
    }

    const addr = addressMap[name];
    if (!addr) {
      console.error(`🚨 No address found for ${name}, skipping.`);
      continue;
    }
     console.log(" → args for PackageManager:", args);
    await verifyContract(addr, args, name);
  }
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
