name: Deploy to Testnet

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        BSC_RPC: ${{ secrets.BSC_RPC }}
        BSC_CHAIN_ID: ${{ secrets.BSC_CHAIN_ID }}
        PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
    
    - name: Deploy contracts to BSC Testnet
      run: npx ts-node -r dotenv/config contracts/scripts/deploy.ts
      env:
        BSC_RPC: ${{ secrets.BSC_RPC }}
        BSC_CHAIN_ID: ${{ secrets.BSC_CHAIN_ID }}
        PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
