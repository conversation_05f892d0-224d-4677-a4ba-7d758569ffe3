name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        BSC_RPC: ${{ secrets.BSC_RPC }}
        BSC_CHAIN_ID: ${{ secrets.BSC_CHAIN_ID }}
        PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
    
    - name: Check code coverage
      run: npm test -- --coverage
