import {
  publicClient,
  getBlockNumber,
  walletClient,
  getAccountNonce,
  getWalletAddress,
  sendTx
} from '../src/services/web3';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// Mock viem functions
jest.mock('viem', () => {
  const originalModule = jest.requireActual('viem');
  return {
    ...originalModule,
    createPublicClient: jest.fn(),
    createWalletClient: jest.fn(),
    http: jest.fn()
  };
});

// Mock the web3 module
jest.mock('../src/services/web3', () => {
  const mockWalletClient = {
    account: {
      address: '******************************************'
    }
  };

  return {
    publicClient: {
      getChainId: jest.fn().mockResolvedValue(97),
      getBlockNumber: jest.fn().mockResolvedValue(BigInt(********)),
      getTransactionCount: jest.fn().mockResolvedValue(BigInt(42))
    },
    walletClient: mockWalletClient,
    getBlockNumber: jest.fn().mockResolvedValue(BigInt(********)),
    getAccountNonce: jest.fn().mockResolvedValue(BigInt(42)),
    getWalletAddress: jest.fn().mockReturnValue('******************************************'),
    sendTx: jest.fn().mockResolvedValue('0xtxhash')
  };
});

// Mock for environment variables
const originalEnv = { ...process.env };

describe('Web3 Service', () => {
  // Restore original environment after tests
  afterEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  describe('Public Client', () => {
    it('connects to BSC Testnet and returns a block number', async () => {
      // Ensure we're pointing at chain ID 97
      const chainId = await publicClient.getChainId();
      expect(chainId).toBe(97);

      // Fetch the latest block
      const block = await getBlockNumber();
      expect(typeof block).toBe('bigint');
      expect(block > 0n).toBe(true);
    });

    it('handles RPC connection failures gracefully', async () => {
      // Mock createPublicClient to return a client that throws an error
      const mockBadClient = {
        getBlockNumber: jest.fn().mockRejectedValue(new Error('Connection failed'))
      };

      (createPublicClient as jest.Mock).mockReturnValue(mockBadClient);

      // Create a client with an invalid RPC URL
      const badClient = createPublicClient({
        chain: bscTestnet,
        transport: http('https://invalid-rpc-url.example.com'),
      });

      // Expect the call to fail with a specific error
      await expect(badClient.getBlockNumber()).rejects.toThrow('Connection failed');
    });
  });

  describe('Wallet Client', () => {
    beforeEach(() => {
      // Ensure PRIVATE_KEY is set for these tests
      process.env.PRIVATE_KEY = 'testprivatekey';
    });

    it('has a valid account when PRIVATE_KEY is provided', () => {
      // Check if we have a wallet account
      expect(walletClient.account).toBeDefined();

      // Get wallet address
      const address = getWalletAddress();
      expect(address).toMatch(/^0x[a-fA-F0-9]{40}$/);
    });

    it('can fetch account nonce when PRIVATE_KEY is provided', async () => {
      // Get account nonce
      const nonce = await getAccountNonce();
      expect(typeof nonce).toBe('bigint');
      expect(nonce >= 0n).toBe(true);
    });

    it('throws error when trying to get wallet address without PRIVATE_KEY', async () => {
      // Create a custom mock for this test
      const mockGetWalletAddress = jest.fn().mockImplementation(() => {
        throw new Error('No PRIVATE_KEY configured for walletClient');
      });

      // Expect error when trying to get wallet address
      expect(() => {
        mockGetWalletAddress();
      }).toThrow('No PRIVATE_KEY configured for walletClient');
    });

    it('throws error when trying to get account nonce without PRIVATE_KEY', async () => {
      // Create a custom mock for this test
      const mockGetAccountNonce = jest.fn().mockRejectedValue(
        new Error('No PRIVATE_KEY configured for walletClient')
      );

      // Expect error when trying to get nonce
      await expect(mockGetAccountNonce()).rejects.toThrow('No PRIVATE_KEY configured for walletClient');
    });

    it('throws error when trying to send transaction without PRIVATE_KEY', async () => {
      // Create a custom mock for this test
      const mockSendTx = jest.fn().mockRejectedValue(
        new Error('No PRIVATE_KEY configured for walletClient')
      );

      // Expect error when trying to send transaction
      await expect(mockSendTx({
        to: '0x********90********90********90********90'
      })).rejects.toThrow('No PRIVATE_KEY configured for walletClient');
    });
  });
});
