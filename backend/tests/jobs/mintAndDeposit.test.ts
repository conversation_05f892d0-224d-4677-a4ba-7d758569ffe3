// backend/tests/jobs/mintAndDeposit.test.ts
import { mintAndDeposit } from '../../src/jobs/mintAndDeposit';
import { updatePaymentStatus } from '../../src/utils/db';
import { sendTx } from '../../src/services/web3';

// Mock dependencies
jest.mock('../../src/utils/db');
jest.mock('../../src/services/web3');

const mockedUpdatePaymentStatus = updatePaymentStatus as jest.MockedFunction<typeof updatePaymentStatus>;
const mockedSendTx = sendTx as jest.MockedFunction<typeof sendTx>;

// Increase the default timeout for all tests in this file
jest.setTimeout(30000);

describe('mintAndDeposit Job', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Use real timers instead of fake ones
    jest.useRealTimers();
  });

  afterEach(() => {
    // No need to clear timers when using real timers
  });

  it('should process a payment and mint tokens successfully', async () => {
    // Mock updatePaymentStatus
    mockedUpdatePaymentStatus.mockResolvedValue({
      paymentId: 'test-payment-id',
      userAddress: '0x123',
      packageId: 'premium',
      method: 'MPESA',
      amountKES: 5000,
      status: 'processing',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Call the function
    const result = await mintAndDeposit({
      paymentId: 'test-payment-id',
      userAddress: '0x123',
      packageId: 'premium',
      amountKES: 5000
    });

    // Assertions
    expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
      'test-payment-id',
      'processing'
    );

    // Second call should update to confirmed with txHash
    expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
      'test-payment-id',
      'confirmed',
      expect.stringMatching(/^0x[a-f0-9]{64}$/)
    );

    expect(result).toEqual({
      success: true,
      paymentId: 'test-payment-id',
      txHash: expect.stringMatching(/^0x[a-f0-9]{64}$/),
      tokenAmount: expect.any(BigInt)
    });

    // Verify token amount calculation (10 tokens per KES)
    expect(result.tokenAmount).toBe(BigInt(50000));
  });

  it('should handle errors during minting process', async () => {
    // Mock updatePaymentStatus for the first call
    mockedUpdatePaymentStatus.mockResolvedValueOnce({
      paymentId: 'test-payment-id',
      userAddress: '0x123',
      packageId: 'premium',
      method: 'MPESA',
      amountKES: 5000,
      status: 'processing',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Mock updatePaymentStatus to throw an error on the second call
    mockedUpdatePaymentStatus.mockRejectedValueOnce(new Error('Database error'));

    // Call the function and expect it to throw
    await expect(mintAndDeposit({
      paymentId: 'test-payment-id',
      userAddress: '0x123',
      packageId: 'premium',
      amountKES: 5000
    })).rejects.toThrow('Database error');

    // Verify the first call was made
    expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
      'test-payment-id',
      'processing'
    );

    // Verify the status was updated to failed
    expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
      'test-payment-id',
      'failed'
    );
  });

  it('should calculate token amount based on package and KES amount', async () => {
    // Mock updatePaymentStatus
    mockedUpdatePaymentStatus.mockResolvedValue({
      paymentId: 'test-payment-id',
      userAddress: '0x123',
      packageId: 'basic',
      method: 'MPESA',
      amountKES: 1000,
      status: 'processing',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Call the function with different amounts
    const result1 = await mintAndDeposit({
      paymentId: 'test-payment-id-1',
      userAddress: '0x123',
      packageId: 'basic',
      amountKES: 1000
    });

    const result2 = await mintAndDeposit({
      paymentId: 'test-payment-id-2',
      userAddress: '0x123',
      packageId: 'premium',
      amountKES: 5000
    });

    // Assertions
    expect(result1.tokenAmount).toBe(BigInt(10000)); // 1000 KES * 10
    expect(result2.tokenAmount).toBe(BigInt(50000)); // 5000 KES * 10
  });
});
