// backend/tests/api/payments.test.ts
import request from 'supertest';
import express from 'express';
import paymentsRouter from '../../src/api/payments';
import {
  initiatePayment,
  confirmPayment,
  getStatus,
  mpesaService,
  pesapalService,
  cryptoService
} from '../../src/services/payments';
import { clearPayments } from '../../src/utils/db';

// Mock dependencies
jest.mock('../../src/services/payments');
jest.mock('express-rate-limit', () => {
  return {
    rateLimit: jest.fn(() => (_req: any, _res: any, next: any) => next())
  };
});

// Mock the ZodError for validation tests
jest.mock('zod', () => {
  const originalModule = jest.requireActual('zod');
  return {
    ...originalModule,
    ZodError: class ZodError extends Error {
      errors: any[];
      constructor(errors: any[]) {
        super('Validation error');
        this.errors = errors;
      }
    }
  };
});

// Set up mocked functions
const mockedInitiatePayment = initiatePayment as jest.MockedFunction<typeof initiatePayment>;
const mockedConfirmPayment = confirmPayment as jest.MockedFunction<typeof confirmPayment>;
const mockedGetStatus = getStatus as jest.MockedFunction<typeof getStatus>;

// No need for spyOn, we'll use the mock functions directly

// Create Express app for testing
const app = express();
app.use(express.json());
app.use('/api/payments', paymentsRouter);

describe('Payments API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clearPayments();
  });

  describe('POST /api/payments/initiate', () => {
    it('should initiate an M-Pesa payment successfully', async () => {
      // Mock the service response for M-Pesa
      mockedInitiatePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        instructions: {
          checkoutRequestID: 'ws_CO_123456789',
          phoneNumber: '************'
        }
      });

      // Make the request
      const response = await request(app)
        .post('/api/payments/initiate')
        .send({
          userAddress: '0x123456789abcdef',
          packageId: 'premium',
          paymentMethod: 'MPESA',
          amountKES: 5000,
          phoneNumber: '************'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        instructions: {
          checkoutRequestID: 'ws_CO_123456789',
          phoneNumber: '************'
        }
      });

      // Verify the function was called
      expect(mockedInitiatePayment).toHaveBeenCalled();
    });

    it('should initiate a Pesapal (BANK/CARD) payment successfully', async () => {
      // Mock the service response for Pesapal
      mockedInitiatePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        paymentLink: 'https://example.com/pay',
        instructions: {
          message: 'Click the payment link to complete your payment',
          expiresAt: '2023-05-08T12:00:00.000Z'
        }
      });

      // Make the request
      const response = await request(app)
        .post('/api/payments/initiate')
        .send({
          userAddress: '0x123456789abcdef',
          packageId: 'premium',
          paymentMethod: 'BANK',
          amountKES: 5000,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          phoneNumber: '************'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        paymentLink: 'https://example.com/pay',
        instructions: {
          message: 'Click the payment link to complete your payment',
          expiresAt: '2023-05-08T12:00:00.000Z'
        }
      });

      // Verify the function was called
      expect(mockedInitiatePayment).toHaveBeenCalled();
    });

    it('should initiate a Crypto payment successfully', async () => {
      // Mock the service response for Crypto
      mockedInitiatePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        instructions: {
          depositAddress: '0xTestAddress',
          amountUSDT: '37.500000',
          currency: 'USDT',
          network: 'BSC',
          minConfirmations: 1
        }
      });

      // Make the request
      const response = await request(app)
        .post('/api/payments/initiate')
        .send({
          userAddress: '0x123456789abcdef',
          packageId: 'premium',
          paymentMethod: 'CRYPTO',
          amountKES: 5000
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        instructions: {
          depositAddress: '0xTestAddress',
          amountUSDT: '37.500000',
          currency: 'USDT',
          network: 'BSC',
          minConfirmations: 1
        }
      });

      // Verify the function was called
      expect(mockedInitiatePayment).toHaveBeenCalled();
    });

    it('should return validation error for invalid input', async () => {
      // Mock the initiatePaymentSchema.parse to throw a ZodError
      const mockZodError = new Error('Validation error');
      mockZodError.name = 'ZodError';
      (mockZodError as any).errors = [
        { path: ['userAddress'], message: 'Invalid address' },
        { path: ['paymentMethod'], message: 'Invalid payment method' },
        { path: ['amountKES'], message: 'Amount must be positive' }
      ];

      // Mock the initiatePayment to throw the ZodError
      mockedInitiatePayment.mockImplementationOnce(() => {
        throw mockZodError;
      });

      // Make the request with invalid data
      const response = await request(app)
        .post('/api/payments/initiate')
        .send({
          userAddress: 'invalid-address', // Missing 0x prefix
          packageId: 'premium',
          paymentMethod: 'INVALID', // Invalid payment method (not in enum)
          amountKES: -100 // Negative amount
        });

      // Assertions
      expect(response.body).toEqual({
        success: false,
        error: 'Internal server error',
        message: 'Validation error'
      });

      // No need to verify the details since we're mocking the error
    });

    it('should handle service errors', async () => {
      // Mock the service to throw an error
      mockedInitiatePayment.mockRejectedValueOnce(new Error('Service error'));

      // Make the request
      const response = await request(app)
        .post('/api/payments/initiate')
        .send({
          userAddress: '0x123456789abcdef',
          packageId: 'premium',
          paymentMethod: 'MPESA',
          amountKES: 5000
        });

      // Assertions
      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: 'Internal server error',
        message: 'Service error'
      });
    });
  });

  describe('POST /api/payments/confirm', () => {
    it('should confirm a payment successfully', async () => {
      // Mock the service response
      mockedConfirmPayment.mockResolvedValueOnce({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        message: 'Payment confirmed and token minting initiated'
      });

      // Make the request
      const response = await request(app)
        .post('/api/payments/confirm')
        .send({
          paymentId: '123e4567-e89b-12d3-a456-************', // Valid UUID format
          transactionRef: 'tx-123456'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        message: 'Payment confirmed and token minting initiated'
      });

      // Verify the function was called
      expect(mockedConfirmPayment).toHaveBeenCalled();
    });

    it('should return validation error for invalid payment ID', async () => {
      // Mock the confirmPaymentSchema.parse to throw a ZodError
      const mockZodError = new Error('Validation error');
      mockZodError.name = 'ZodError';
      (mockZodError as any).errors = [
        { path: ['paymentId'], message: 'Invalid UUID format' }
      ];

      // Mock the confirmPayment to throw the ZodError
      mockedConfirmPayment.mockImplementationOnce(() => {
        throw mockZodError;
      });

      // Make the request with invalid data
      const response = await request(app)
        .post('/api/payments/confirm')
        .send({
          paymentId: 'invalid-uuid', // Invalid UUID format
          transactionRef: 'tx-123456'
        });

      // Assertions
      expect(response.body).toEqual({
        success: false,
        error: 'Internal server error',
        message: 'Validation error'
      });

      // No need to verify the details since we're mocking the error
    });

    it('should handle service errors during confirmation', async () => {
      // Mock the service to throw an error
      mockedConfirmPayment.mockRejectedValueOnce(new Error('Confirmation failed'));

      // Make the request
      const response = await request(app)
        .post('/api/payments/confirm')
        .send({
          paymentId: '123e4567-e89b-12d3-a456-************',
          transactionRef: 'tx-123456'
        });

      // Assertions
      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: 'Internal server error',
        message: 'Confirmation failed'
      });
    });
  });

  describe('POST /api/payments/mpesa/callback', () => {
    // Mock the mpesaService
    const mpesaService = jest.requireMock('../../src/services/payments').mpesaService;
    mpesaService.handleMpesaCallback = jest.fn();

    it('should handle M-Pesa callback successfully', async () => {
      // Mock the service response
      mpesaService.handleMpesaCallback.mockImplementationOnce((req: any, res: any) => {
        res.status(200).json({ success: true });
      });

      // Make the request with M-Pesa callback data
      const response = await request(app)
        .post('/api/payments/mpesa/callback')
        .send({
          Body: {
            stkCallback: {
              MerchantRequestID: 'merchant-123',
              CheckoutRequestID: 'checkout-123',
              ResultCode: 0,
              ResultDesc: 'Success',
              CallbackMetadata: {
                Item: [
                  { Name: 'Amount', Value: 5000 },
                  { Name: 'MpesaReceiptNumber', Value: 'MPESA123456' },
                  { Name: 'AccountReference', Value: 'test-payment-id' }
                ]
              }
            }
          }
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ success: true });
      expect(mpesaService.handleMpesaCallback).toHaveBeenCalled();
    });
  });

  describe('POST /api/payments/pesapal/ipn', () => {
    // Mock the pesapalService
    const pesapalService = jest.requireMock('../../src/services/payments').pesapalService;
    pesapalService.handlePesapalIPN = jest.fn();

    it('should handle Pesapal IPN successfully', async () => {
      // Mock the service response
      pesapalService.handlePesapalIPN.mockImplementationOnce((req: any, res: any) => {
        res.status(200).json({ success: true });
      });

      // Make the request with Pesapal IPN data
      const response = await request(app)
        .post('/api/payments/pesapal/ipn')
        .send({
          OrderTrackingId: 'tracking-123',
          OrderMerchantReference: 'test-payment-id',
          PaymentStatus: 'COMPLETED'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ success: true });
      expect(pesapalService.handlePesapalIPN).toHaveBeenCalled();
    });
  });

  describe('GET /api/payments/status/:paymentId', () => {
    it('should get payment status successfully', async () => {
      // Mock the service response
      mockedGetStatus.mockResolvedValueOnce({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        method: 'MPESA',
        amountKES: 5000,
        userAddress: '0x123',
        packageId: 'premium',
        transactionRef: 'tx-123456',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Make the request
      const response = await request(app)
        .get('/api/payments/status/test-payment-id');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        method: 'MPESA',
        amountKES: 5000,
        userAddress: '0x123',
        packageId: 'premium',
        transactionRef: 'tx-123456',
        createdAt: expect.any(String),
        updatedAt: expect.any(String)
      });

      expect(mockedGetStatus).toHaveBeenCalledWith('test-payment-id');
    });

    it('should handle payment not found', async () => {
      // Mock the service response
      mockedGetStatus.mockResolvedValueOnce({
        success: false,
        message: 'Payment not found'
      });

      // Make the request
      const response = await request(app)
        .get('/api/payments/status/non-existent-id');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: false,
        message: 'Payment not found'
      });
    });
  });

  describe('POST /api/payments/crypto/check', () => {
    // Mock the cryptoService
    const cryptoService = jest.requireMock('../../src/services/payments').cryptoService;
    cryptoService.checkTransfer = jest.fn();

    it('should check crypto transaction status successfully', async () => {
      // Mock the service response
      cryptoService.checkTransfer.mockResolvedValueOnce({
        success: true,
        paymentId: 'test-payment-id',
        txHash: '0x1234567890abcdef',
        message: 'Transfer verified and payment confirmed'
      });

      // Make the request
      const response = await request(app)
        .post('/api/payments/crypto/check')
        .send({
          paymentId: '123e4567-e89b-12d3-a456-************',
          txHash: '0x1234567890abcdef'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        txHash: '0x1234567890abcdef',
        message: 'Transfer verified and payment confirmed'
      });

      expect(cryptoService.checkTransfer).toHaveBeenCalledWith(
        '123e4567-e89b-12d3-a456-************',
        '0x1234567890abcdef'
      );
    });

    it('should handle failed crypto transaction verification', async () => {
      // Mock the service response
      cryptoService.checkTransfer.mockResolvedValueOnce({
        success: false,
        paymentId: 'test-payment-id',
        txHash: '0x1234567890abcdef',
        message: 'Failed to verify transfer'
      });

      // Make the request
      const response = await request(app)
        .post('/api/payments/crypto/check')
        .send({
          paymentId: '123e4567-e89b-12d3-a456-************',
          txHash: '0x1234567890abcdef'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: false,
        paymentId: 'test-payment-id',
        txHash: '0x1234567890abcdef',
        message: 'Failed to verify transfer'
      });
    });
  });
});
