// backend/tests/services/payments/mpesa.test.ts
import axios from 'axios';
import { initiatePayment, handleMpesaCallback } from '../../../src/services/payments/mpesa';
import { savePayment, updatePaymentStatus, clearPayments } from '../../../src/utils/db';
import { confirmPayment } from '../../../src/services/payments/common';

// Mock dependencies
jest.mock('axios');
jest.mock('../../../src/utils/db');
jest.mock('../../../src/services/payments/common');

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedSavePayment = savePayment as jest.MockedFunction<typeof savePayment>;
const mockedUpdatePaymentStatus = updatePaymentStatus as jest.MockedFunction<typeof updatePaymentStatus>;
const mockedConfirmPayment = confirmPayment as jest.MockedFunction<typeof confirmPayment>;

describe('M-Pesa Payment Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clearPayments();

    // Mock environment variables
    process.env.MPESA_CONSUMER_KEY = 'test-consumer-key';
    process.env.MPESA_CONSUMER_SECRET = 'test-consumer-secret';
    process.env.MPESA_SHORTCODE = '174379';
    process.env.MPESA_PASSKEY = 'test-passkey';
    process.env.BACKEND_URL = 'https://api.example.com';
  });

  describe('initiatePayment', () => {
    it('should initiate an M-Pesa payment successfully', async () => {
      // Mock axios responses
      mockedAxios.get.mockResolvedValueOnce({
        data: { access_token: 'test-access-token' }
      });

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          CheckoutRequestID: 'ws_CO_123456789',
          ResponseCode: '0',
          ResponseDescription: 'Success. Request accepted for processing',
          CustomerMessage: 'Success. Request accepted for processing'
        }
      });

      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'basic',
        method: 'MPESA',
        amountKES: 1000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await initiatePayment({
        userAddress: '0x123',
        packageId: 'basic',
        amountKES: 1000,
        phoneNumber: '254712345678'
      });

      // Assertions
      expect(mockedSavePayment).toHaveBeenCalledWith(expect.objectContaining({
        userAddress: '0x123',
        packageId: 'basic',
        method: 'MPESA',
        amountKES: 1000,
        status: 'pending'
      }));

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
        expect.any(Object)
      );

      // Verify axios.post was called with the correct URL
      expect(mockedAxios.post.mock.calls[0][0]).toBe(
        'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest'
      );

      // Verify the payload contains the expected properties
      const payload = mockedAxios.post.mock.calls[0][1];
      expect(payload).toMatchObject({
        BusinessShortCode: '174379',
        TransactionType: 'CustomerPayBillOnline',
        Amount: 1000,
        PartyA: '254712345678',
        PhoneNumber: '254712345678',
        CallBackURL: process.env.MPESA_CALLBACK_URL
      });

      // Verify headers were passed
      expect(mockedAxios.post.mock.calls[0][2]).toHaveProperty('headers');

      expect(result).toEqual({
        paymentId: expect.any(String),
        instructions: {
          checkoutRequestID: 'ws_CO_123456789',
          phoneNumber: '254712345678'
        }
      });
    });

    it('should handle errors during payment initiation', async () => {
      // Mock axios to throw an error
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      // Call the function and expect it to throw
      await expect(initiatePayment({
        userAddress: '0x123',
        packageId: 'basic',
        amountKES: 1000
      })).rejects.toThrow('Network error');

      // Verify savePayment was still called
      expect(mockedSavePayment).toHaveBeenCalled();
    });
  });

  describe('handleMpesaCallback', () => {
    it('should handle a successful M-Pesa callback', async () => {
      // Mock request and response
      const req = {
        body: {
          Body: {
            stkCallback: {
              MerchantRequestID: 'merchant-123',
              CheckoutRequestID: 'checkout-123',
              ResultCode: 0,
              ResultDesc: 'The service request is processed successfully.',
              CallbackMetadata: {
                Item: [
                  { Name: 'Amount', Value: 1000 },
                  { Name: 'MpesaReceiptNumber', Value: 'LHG31AA5TX' },
                  { Name: 'AccountReference', Value: 'test-payment-id' }
                ]
              }
            }
          }
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handleMpesaCallback(req as any, res as any);

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'confirmed',
        'LHG31AA5TX'
      );

      expect(mockedConfirmPayment).toHaveBeenCalledWith(
        'test-payment-id',
        'LHG31AA5TX'
      );

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle a failed M-Pesa callback', async () => {
      // Mock request and response
      const req = {
        body: {
          Body: {
            stkCallback: {
              MerchantRequestID: 'merchant-123',
              CheckoutRequestID: 'checkout-123',
              ResultCode: 1032,
              ResultDesc: 'Request cancelled by user',
              CallbackMetadata: {
                Item: [
                  { Name: 'AccountReference', Value: 'test-payment-id' }
                ]
              }
            }
          }
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handleMpesaCallback(req as any, res as any);

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'failed'
      );

      expect(mockedConfirmPayment).not.toHaveBeenCalled();

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });
  });
});
