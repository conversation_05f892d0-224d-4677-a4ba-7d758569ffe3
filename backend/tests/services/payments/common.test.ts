// backend/tests/services/payments/common.test.ts
import { confirmPayment, getPaymentStatus } from '../../../src/services/payments/common';
import { updatePaymentStatus, getPayment } from '../../../src/utils/db';
import { mintAndDeposit } from '../../../src/jobs/mintAndDeposit';

// Mock dependencies
jest.mock('../../../src/utils/db');
jest.mock('../../../src/jobs/mintAndDeposit');

const mockedUpdatePaymentStatus = updatePaymentStatus as jest.MockedFunction<typeof updatePaymentStatus>;
const mockedGetPayment = getPayment as jest.MockedFunction<typeof getPayment>;
const mockedMintAndDeposit = mintAndDeposit as jest.MockedFunction<typeof mintAndDeposit>;

describe('Common Payment Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Use real timers instead of fake ones
    jest.useRealTimers();
  });

  describe('confirmPayment', () => {
    it('should confirm a payment and trigger minting process', async () => {
      // Mock updatePaymentStatus
      mockedUpdatePaymentStatus.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'MPESA',
        amountKES: 5000,
        status: 'confirmed',
        transactionRef: 'tx-123456',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await confirmPayment('test-payment-id', 'tx-123456');

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'confirmed',
        'tx-123456'
      );

      // Wait for any pending promises to resolve
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verify mintAndDeposit was called with correct parameters
      expect(mockedMintAndDeposit).toHaveBeenCalledWith({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        amountKES: 5000
      });

      expect(result).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        message: 'Payment confirmed and token minting initiated'
      });
    });

    it('should handle payment not found error', async () => {
      // Mock updatePaymentStatus to throw an error (payment not found)
      mockedUpdatePaymentStatus.mockRejectedValueOnce(new Error('Payment not found'));

      // Call the function
      const result = await confirmPayment('non-existent-id');

      // Assertions
      expect(result).toEqual({
        success: false,
        paymentId: 'non-existent-id',
        status: 'failed',
        message: 'Payment confirmation failed'
      });

      // Verify updatePaymentStatus was called to mark as failed
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'non-existent-id',
        'failed'
      );
    });

    it('should handle errors during confirmation', async () => {
      // Mock updatePaymentStatus to throw an error
      mockedUpdatePaymentStatus.mockRejectedValueOnce(new Error('Database error'));

      // Call the function
      const result = await confirmPayment('test-payment-id');

      // Assertions
      expect(result).toEqual({
        success: false,
        paymentId: 'test-payment-id',
        status: 'failed',
        message: 'Payment confirmation failed'
      });

      // Verify updatePaymentStatus was called to mark as failed
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'failed'
      );
    });

    it('should handle errors in the minting process', async () => {
      // Mock updatePaymentStatus
      mockedUpdatePaymentStatus.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'MPESA',
        amountKES: 5000,
        status: 'confirmed',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Mock mintAndDeposit to throw an error
      mockedMintAndDeposit.mockRejectedValueOnce(new Error('Minting error'));

      // Call the function
      const result = await confirmPayment('test-payment-id');

      // Wait for any pending promises to resolve
      await new Promise(resolve => setTimeout(resolve, 500));

      // Assertions
      expect(result).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        message: 'Payment confirmed and token minting initiated'
      });

      // Verify updatePaymentStatus was called to mark as failed after minting error
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'failed'
      );
    });
  });

  describe('getPaymentStatus', () => {
    it('should return payment status when payment exists', async () => {
      // Mock getPayment
      mockedGetPayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'MPESA',
        amountKES: 5000,
        status: 'confirmed',
        transactionRef: 'tx-123456',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await getPaymentStatus('test-payment-id');

      // Assertions
      expect(mockedGetPayment).toHaveBeenCalledWith('test-payment-id');

      expect(result).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        method: 'MPESA',
        amountKES: 5000,
        userAddress: '0x123',
        packageId: 'premium',
        transactionRef: 'tx-123456',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      });
    });

    it('should handle payment not found', async () => {
      // Mock getPayment to return null
      mockedGetPayment.mockResolvedValueOnce(null);

      // Call the function
      const result = await getPaymentStatus('non-existent-id');

      // Assertions
      expect(result).toEqual({
        success: false,
        message: 'Payment not found'
      });
    });
  });
});
