// backend/tests/services/payments/crypto.test.ts
import { initiatePayment, checkTransfer } from '../../../src/services/payments/crypto';
import { savePayment, updatePaymentStatus, clearPayments } from '../../../src/utils/db';
import { confirmPayment } from '../../../src/services/payments/common';
import { getWalletAddress } from '../../../src/services/web3';

// Mock dependencies
jest.mock('../../../src/utils/db');
jest.mock('../../../src/services/payments/common');
jest.mock('../../../src/services/web3');

const mockedSavePayment = savePayment as jest.MockedFunction<typeof savePayment>;
const mockedUpdatePaymentStatus = updatePaymentStatus as jest.MockedFunction<typeof updatePaymentStatus>;
const mockedConfirmPayment = confirmPayment as jest.MockedFunction<typeof confirmPayment>;
const mockedGetWalletAddress = getWalletAddress as jest.MockedFunction<typeof getWalletAddress>;

// Mock setTimeout
jest.useFakeTimers();

describe('Crypto Payment Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clearPayments();

    // Mock environment variables
    process.env.USDT_CONTRACT_ADDRESS = '******************************************';
    process.env.DEPOSIT_ADDRESS = '0xDepositAddress123456789';

    // Mock getWalletAddress
    mockedGetWalletAddress.mockReturnValue('******************************************');
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('initiatePayment', () => {
    it('should initiate a crypto payment successfully', async () => {
      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'gold',
        method: 'CRYPTO',
        amountKES: 10000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await initiatePayment({
        userAddress: '0x123',
        packageId: 'gold',
        amountKES: 10000
      });

      // Assertions
      expect(mockedSavePayment).toHaveBeenCalledWith(expect.objectContaining({
        userAddress: '0x123',
        packageId: 'gold',
        method: 'CRYPTO',
        amountKES: 10000,
        status: 'pending'
      }));

      expect(result).toEqual({
        paymentId: expect.any(String),
        instructions: {
          depositAddress: '******************************************',
          amountUSDT: '75.000000',
          currency: 'USDT',
          network: 'BSC',
          minConfirmations: 1
        }
      });
    });

    it('should use wallet address if deposit address is not configured', async () => {
      // Save original deposit address
      const originalDepositAddress = process.env.DEPOSIT_ADDRESS;

      // Temporarily remove DEPOSIT_ADDRESS from environment
      delete process.env.DEPOSIT_ADDRESS;

      // Mock the implementation of getWalletAddress directly in the crypto module
      jest.mock('../../../src/services/payments/crypto', () => {
        const originalModule = jest.requireActual('../../../src/services/payments/crypto');
        return {
          ...originalModule,
          getWalletAddress: jest.fn().mockReturnValue('******************************************')
        };
      });

      // Re-import the module to get the mocked version
      const { initiatePayment: mockedInitiatePayment } = require('../../../src/services/payments/crypto');

      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'gold',
        method: 'CRYPTO',
        amountKES: 10000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await mockedInitiatePayment({
        userAddress: '0x123',
        packageId: 'gold',
        amountKES: 10000
      });

      // Assertions
      expect(result.instructions.depositAddress).toBe('******************************************');

      // Restore original deposit address
      process.env.DEPOSIT_ADDRESS = originalDepositAddress;

      // Clear the mock to avoid affecting other tests
      jest.resetModules();
    });

    it('should start a transfer listener that confirms payment after timeout', async () => {
      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'gold',
        method: 'CRYPTO',
        amountKES: 10000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      await initiatePayment({
        userAddress: '0x123',
        packageId: 'gold',
        amountKES: 10000
      });

      // Fast-forward time to trigger the simulated transfer
      jest.advanceTimersByTime(10000);

      // Wait for any pending promises to resolve
      await Promise.resolve();

      // Assertions
      expect(mockedConfirmPayment).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringMatching(/^0x[a-f0-9]{64}$/)
      );
    });
  });

  describe('checkTransfer', () => {
    it('should confirm a payment when transaction hash is provided', async () => {
      // Mock confirmPayment
      mockedConfirmPayment.mockResolvedValueOnce({
        success: true,
        paymentId: 'test-payment-id',
        status: 'confirmed',
        message: 'Payment confirmed and token minting initiated'
      });

      // Call the function
      const result = await checkTransfer('test-payment-id', '0xTransactionHash123');

      // Assertions
      expect(mockedConfirmPayment).toHaveBeenCalledWith(
        'test-payment-id',
        '0xTransactionHash123'
      );

      expect(result).toEqual({
        success: true,
        paymentId: 'test-payment-id',
        txHash: '0xTransactionHash123',
        message: 'Transfer verified and payment confirmed'
      });
    });

    it('should handle errors during transfer verification', async () => {
      // Mock confirmPayment to throw an error
      mockedConfirmPayment.mockRejectedValueOnce(new Error('Verification failed'));

      // Call the function
      const result = await checkTransfer('test-payment-id', '0xTransactionHash123');

      // Assertions
      expect(result).toEqual({
        success: false,
        paymentId: 'test-payment-id',
        txHash: '0xTransactionHash123',
        message: 'Failed to verify transfer'
      });
    });
  });
});
