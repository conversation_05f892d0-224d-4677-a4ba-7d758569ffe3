// backend/tests/services/payments/aggregator.test.ts
import axios from 'axios';
import { initiatePayment, handlePesapalIPN, handlePesapalRedirect, checkPesapalStatus } from '../../../src/services/payments/aggregator';
import { savePayment, updatePaymentStatus, clearPayments } from '../../../src/utils/db';
import { confirmPayment } from '../../../src/services/payments/common';

// Mock dependencies
jest.mock('axios');
jest.mock('../../../src/utils/db');
jest.mock('../../../src/services/payments/common');

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedSavePayment = savePayment as jest.MockedFunction<typeof savePayment>;
const mockedUpdatePaymentStatus = updatePaymentStatus as jest.MockedFunction<typeof updatePaymentStatus>;
const mockedConfirmPayment = confirmPayment as jest.MockedFunction<typeof confirmPayment>;

describe('Pesapal Aggregator Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clearPayments();

    // Environment variables are already set in jest.setup.js
  });

  describe('initiatePayment', () => {
    it('should initiate a Pesapal payment successfully', async () => {
      // Mock getAuthToken response
      mockedAxios.post.mockResolvedValueOnce({
        data: { token: 'test-auth-token' }
      });

      // Mock orders API response
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          status: 'SUCCESS',
          message: 'Request processed successfully',
          order_tracking_id: 'tracking-123',
          redirect_url: 'https://pay.pesapal.com/iframe?OrderTrackingId=tracking-123'
        }
      });

      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'PESAPAL',
        amountKES: 5000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await initiatePayment({
        userAddress: '0x123',
        packageId: 'premium',
        amountKES: 5000,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '254712345678'
      });

      // Assertions
      expect(mockedSavePayment).toHaveBeenCalledWith(expect.objectContaining({
        userAddress: '0x123',
        packageId: 'premium',
        method: 'PESAPAL',
        amountKES: 5000,
        status: 'pending'
      }));

      // Verify auth token request
      expect(mockedAxios.post.mock.calls[0][0]).toBe('https://pay.pesapal.com/v3/auth/token');
      expect(mockedAxios.post.mock.calls[0][1]).toEqual({
        consumer_key: 'test-pesapal-consumer-key',
        consumer_secret: 'test-pesapal-consumer-secret'
      });

      // Verify orders API request
      expect(mockedAxios.post.mock.calls[1][0]).toBe('https://pay.pesapal.com/v3/orders');
      expect(mockedAxios.post.mock.calls[1][1]).toMatchObject({
        currency: 'KES',
        amount: 5000,
        description: expect.stringContaining('BlockCoop Share Purchase'),
        callback_url: 'https://api.example.com/api/payments/pesapal/redirect',
        notification_id: 'test-ipn-id',
        billing_address: {
          email_address: '<EMAIL>',
          phone_number: '254712345678',
          first_name: 'Test',
          last_name: 'User'
        }
      });

      expect(result).toEqual({
        paymentId: expect.any(String),
        paymentLink: 'https://pay.pesapal.com/iframe?OrderTrackingId=tracking-123',
        instructions: {
          message: 'Click the payment link to complete your payment',
          expiresAt: expect.any(String)
        }
      });
    });

    it('should handle auth token error', async () => {
      // Mock getAuthToken to throw an error
      mockedAxios.post.mockRejectedValueOnce(new Error('Auth error'));

      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'PESAPAL',
        amountKES: 5000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function and expect it to throw
      await expect(initiatePayment({
        userAddress: '0x123',
        packageId: 'premium',
        amountKES: 5000
      })).rejects.toThrow('Auth error');

      // Verify savePayment was still called
      expect(mockedSavePayment).toHaveBeenCalled();
    });
  });

  describe('handlePesapalIPN', () => {
    it('should handle a successful Pesapal IPN', async () => {
      // Mock request and response
      const req = {
        body: {
          OrderTrackingId: 'tracking-123',
          OrderMerchantReference: 'test-payment-id',
          OrderNotificationType: 'CHANGE',
          PaymentStatus: 'COMPLETED'
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handlePesapalIPN(req as any, res as any);

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'confirmed',
        'tracking-123'
      );

      expect(mockedConfirmPayment).toHaveBeenCalledWith(
        'test-payment-id',
        'tracking-123'
      );

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle a failed Pesapal IPN', async () => {
      // Mock request and response
      const req = {
        body: {
          OrderTrackingId: 'tracking-123',
          OrderMerchantReference: 'test-payment-id',
          OrderNotificationType: 'CHANGE',
          PaymentStatus: 'FAILED'
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handlePesapalIPN(req as any, res as any);

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'failed'
      );

      expect(mockedConfirmPayment).not.toHaveBeenCalled();

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle missing required fields', async () => {
      // Mock request and response with missing fields
      const req = {
        body: {
          OrderTrackingId: 'tracking-123'
          // Missing OrderMerchantReference and PaymentStatus
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handlePesapalIPN(req as any, res as any);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Missing required fields'
      });
    });
  });

  describe('handlePesapalRedirect', () => {
    it('should handle redirect and redirect to payment status page', () => {
      // Mock request and response
      const req = {
        query: {
          OrderMerchantReference: 'test-payment-id',
          OrderTrackingId: 'tracking-123'
        }
      };

      const res = {
        redirect: jest.fn()
      };

      // Call the function
      handlePesapalRedirect(req as any, res as any);

      // Assertions
      expect(res.redirect).toHaveBeenCalledWith('/payment-status?paymentId=test-payment-id');
    });

    it('should handle errors during redirect', () => {
      // Mock request with no query params to trigger error
      const req = {
        query: {}
      };

      const res = {
        redirect: jest.fn()
      };

      // Call the function
      handlePesapalRedirect(req as any, res as any);

      // Assertions
      expect(res.redirect).toHaveBeenCalledWith('/payment-status?paymentId=undefined');
    });
  });

  describe('checkPesapalStatus', () => {
    it('should check transaction status successfully', async () => {
      // Mock getAuthToken response
      mockedAxios.post.mockResolvedValueOnce({
        data: { token: 'test-auth-token' }
      });

      // Mock transaction status response
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          status: 'COMPLETED',
          payment_method: 'CARD',
          amount: 5000,
          created_date: '2023-05-08T12:00:00Z'
        }
      });

      // Call the function
      const result = await checkPesapalStatus('tracking-123');

      // Assertions
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://pay.pesapal.com/v3/auth/token',
        {
          consumer_key: 'test-pesapal-consumer-key',
          consumer_secret: 'test-pesapal-consumer-secret'
        },
        expect.any(Object)
      );

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://pay.pesapal.com/v3/transactions/tracking-123',
        {
          headers: {
            Authorization: 'Bearer test-auth-token'
          }
        }
      );

      expect(result).toEqual({
        status: 'COMPLETED',
        payment_method: 'CARD',
        amount: 5000,
        created_date: '2023-05-08T12:00:00Z'
      });
    });

    it('should handle errors during status check', async () => {
      // Mock getAuthToken response
      mockedAxios.post.mockResolvedValueOnce({
        data: { token: 'test-auth-token' }
      });

      // Mock transaction status to throw an error
      mockedAxios.get.mockRejectedValueOnce(new Error('API error'));

      // Call the function and expect it to throw
      await expect(checkPesapalStatus('tracking-123')).rejects.toThrow('API error');
    });
  });
});
