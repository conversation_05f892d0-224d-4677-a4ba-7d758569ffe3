// backend/tests/services/payments/pesapal.test.ts
import axios from 'axios';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { initiatePayment, handlePesapalIPN, registerIPN } from '../../../src/services/payments/pesapal';
import { savePayment, updatePaymentStatus, clearPayments } from '../../../src/utils/db';
import { confirmPayment } from '../../../src/services/payments/common';

// Mock dependencies
jest.mock('axios');
jest.mock('jsonwebtoken');
jest.mock('crypto');
jest.mock('../../../src/utils/db');
jest.mock('../../../src/services/payments/common');

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedJwt = jwt as jest.Mocked<typeof jwt>;
const mockedCrypto = crypto as jest.Mocked<typeof crypto>;
const mockedSavePayment = savePayment as jest.MockedFunction<typeof savePayment>;
const mockedUpdatePaymentStatus = updatePaymentStatus as jest.MockedFunction<typeof updatePaymentStatus>;
const mockedConfirmPayment = confirmPayment as jest.MockedFunction<typeof confirmPayment>;

describe('Pesapal Payment Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clearPayments();

    // Mock environment variables
    process.env.PESAPAL_CONSUMER_KEY = 'test-consumer-key';
    process.env.PESAPAL_CONSUMER_SECRET = 'test-consumer-secret';
    process.env.PESAPAL_IPN_URL = 'https://api.example.com/api/payments/pesapal/ipn';
    process.env.BACKEND_URL = 'https://api.example.com';
  });

  describe('registerIPN', () => {
    it('should register IPN URL with Pesapal successfully', async () => {
      // Mock crypto for HMAC signature
      const mockHmac = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('test-signature')
      };
      mockedCrypto.createHmac.mockReturnValue(mockHmac as any);

      // Mock axios response
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          status: 'SUCCESS',
          message: 'IPN URL registered successfully'
        }
      });

      // Call the function
      const result = await registerIPN();

      // Assertions
      expect(mockedCrypto.createHmac).toHaveBeenCalledWith(
        'sha1',
        'test-pesapal-consumer-secret'
      );

      expect(mockHmac.update).toHaveBeenCalledWith(
        JSON.stringify({
          url: 'http://localhost:3000/api/payments/pesapal/ipn',
          ipn_notification_type: 'POST'
        })
      );

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://cybqa.pesapal.com/API/RegisterIPNURL',
        {
          url: 'http://localhost:3000/api/payments/pesapal/ipn',
          ipn_notification_type: 'POST'
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Basic '),
            'Pesapal-Notification-HMAC-SHA1': 'test-signature'
          })
        })
      );

      expect(result).toEqual({
        status: 'SUCCESS',
        message: 'IPN URL registered successfully'
      });
    });

    it('should handle errors during IPN registration', async () => {
      // Mock crypto for HMAC signature
      const mockHmac = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('test-signature')
      };
      mockedCrypto.createHmac.mockReturnValue(mockHmac as any);

      // Mock axios to throw an error
      mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

      // Call the function and expect it to throw
      await expect(registerIPN()).rejects.toThrow('Network error');
    });
  });

  describe('initiatePayment', () => {
    it('should initiate a Pesapal payment successfully', async () => {
      // Mock JWT sign
      mockedJwt.sign.mockImplementationOnce(() => 'test-token');

      // Mock axios response
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          status: 'SUCCESS',
          message: 'Request processed successfully',
          order_tracking_id: 'tracking-123',
          iframe_url: 'https://checkout.pesapal.com/iframe?OrderTrackingId=tracking-123'
        }
      });

      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'PESAPAL',
        amountKES: 5000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function
      const result = await initiatePayment({
        userAddress: '0x123',
        packageId: 'premium',
        amountKES: 5000,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '254712345678'
      });

      // Assertions
      expect(mockedSavePayment).toHaveBeenCalledWith(expect.objectContaining({
        userAddress: '0x123',
        packageId: 'premium',
        method: 'PESAPAL',
        amountKES: 5000,
        status: 'pending'
      }));

      expect(mockedJwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: 5000,
          currency: 'KES',
          type: 'MERCHANT',
          first_name: 'Test',
          last_name: 'User',
          email: '<EMAIL>',
          phone_number: '254712345678'
        }),
        'test-pesapal-consumer-secret',
        { algorithm: 'HS256' }
      );

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://cybqa.pesapal.com/API/PostPesapalDirectOrderV4',
        expect.objectContaining({
          amount: 5000,
          currency: 'KES',
          token: 'test-token'
        }),
        expect.any(Object)
      );

      expect(result).toEqual({
        paymentId: mockedSavePayment.mock.results[0].value.paymentId,
        paymentLink: 'https://checkout.pesapal.com/iframe?OrderTrackingId=tracking-123',
        instructions: expect.any(Object)
      });
    });

    it('should handle errors during payment initiation', async () => {
      // Mock JWT sign
      mockedJwt.sign.mockImplementationOnce(() => 'test-token');

      // Mock axios to throw an error
      mockedAxios.post.mockRejectedValueOnce(new Error('API error'));

      // Mock savePayment
      mockedSavePayment.mockResolvedValueOnce({
        paymentId: 'test-payment-id',
        userAddress: '0x123',
        packageId: 'premium',
        method: 'PESAPAL',
        amountKES: 5000,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Call the function and expect it to throw
      await expect(initiatePayment({
        userAddress: '0x123',
        packageId: 'premium',
        amountKES: 5000
      })).rejects.toThrow('API error');

      // Verify savePayment was still called
      expect(mockedSavePayment).toHaveBeenCalled();
    });
  });

  describe('handlePesapalIPN', () => {
    it('should handle a successful Pesapal IPN', async () => {
      // Mock request and response
      const req = {
        body: {
          OrderTrackingId: 'tracking-123',
          OrderMerchantReference: 'test-payment-id',
          PaymentStatus: 'COMPLETED'
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handlePesapalIPN(req as any, res as any);

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'confirmed',
        'tracking-123'
      );

      expect(mockedConfirmPayment).toHaveBeenCalledWith(
        'test-payment-id',
        'tracking-123'
      );

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle a failed Pesapal IPN', async () => {
      // Mock request and response
      const req = {
        body: {
          OrderTrackingId: 'tracking-123',
          OrderMerchantReference: 'test-payment-id',
          PaymentStatus: 'FAILED'
        }
      };

      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Call the function
      await handlePesapalIPN(req as any, res as any);

      // Assertions
      expect(mockedUpdatePaymentStatus).toHaveBeenCalledWith(
        'test-payment-id',
        'failed'
      );

      expect(mockedConfirmPayment).not.toHaveBeenCalled();

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });
  });
});
