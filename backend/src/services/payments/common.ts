// backend/src/services/payments/common.ts
import { updatePaymentStatus, getPayment } from '../../utils/db';
import { mintAndDeposit } from '../../jobs/mintAndDeposit';

/**
 * Confirms a payment and triggers the minting and deposit process
 *
 * @param paymentId The unique payment identifier
 * @param transactionRef Optional external transaction reference
 * @returns The result of the confirmation process
 */
export async function confirmPayment(paymentId: string, transactionRef?: string) {
  try {
    // 1. Update payment status in database
    const payment = await updatePaymentStatus(paymentId, 'confirmed', transactionRef);

    if (!payment) {
      throw new Error(`Payment not found: ${paymentId}`);
    }

    // 2. Enqueue the mint and deposit job
    // In a production environment, this would be added to a job queue
    // For now, we'll just call it directly with a small delay to simulate async processing
    setTimeout(() => {
      try {
        // Call mintAndDeposit and handle errors with try/catch
        mintAndDeposit({
          paymentId,
          userAddress: payment.userAddress,
          packageId: payment.packageId,
          amountKES: payment.amountKES
        }).then(() => {
          console.log(`Minting completed for payment ${paymentId}`);
        }).catch((err: Error) => {
          console.error(`Error in mintAndDeposit for payment ${paymentId}:`, err);
          updatePaymentStatus(paymentId, 'failed');
        });
      } catch (err) {
        console.error(`Error in mintAndDeposit for payment ${paymentId}:`, err);
        updatePaymentStatus(paymentId, 'failed');
      }
    }, 100);

    // 3. Return confirmation result
    return {
      success: true,
      paymentId,
      status: 'confirmed',
      message: 'Payment confirmed and token minting initiated'
    };
  } catch (error) {
    console.error(`Error confirming payment ${paymentId}:`, error);

    // Update status to failed if there was an error
    await updatePaymentStatus(paymentId, 'failed');

    return {
      success: false,
      paymentId,
      status: 'failed',
      message: 'Payment confirmation failed'
    };
  }
}

/**
 * Gets the current status of a payment
 *
 * @param paymentId The unique payment identifier
 * @returns The payment status information
 */
export async function getPaymentStatus(paymentId: string) {
  const payment = await getPayment(paymentId);

  if (!payment) {
    return {
      success: false,
      message: 'Payment not found'
    };
  }

  return {
    success: true,
    paymentId,
    status: payment.status,
    method: payment.method,
    amountKES: payment.amountKES,
    userAddress: payment.userAddress,
    packageId: payment.packageId,
    transactionRef: payment.transactionRef,
    createdAt: payment.createdAt,
    updatedAt: payment.updatedAt
  };
}
