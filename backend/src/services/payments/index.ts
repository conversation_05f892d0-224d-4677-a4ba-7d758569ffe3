// backend/src/services/payments/index.ts
import { z } from 'zod';
import * as mpesaService from './mpesa';
import * as pesapalService from './pesapal';
import * as cryptoService from './crypto';
import { confirmPayment as confirmPaymentCommon, getPaymentStatus } from './common';

// Zod schema for payment initiation
export const initiatePaymentSchema = z.object({
  userAddress: z.string().startsWith('0x'),
  packageId: z.string(),
  paymentMethod: z.enum(['MPESA', 'BANK', 'CARD', 'CRYPTO']),
  amountKES: z.number().positive(),
  phoneNumber: z.string().optional(),
  email: z.string().email().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional()
});

// Zod schema for payment confirmation
export const confirmPaymentSchema = z.object({
  paymentId: z.string().uuid(),
  transactionRef: z.string().optional()
});

/**
 * Initiates a payment using the specified payment method
 */
export async function initiatePayment(data: z.infer<typeof initiatePaymentSchema>) {
  const { paymentMethod, userAddress, packageId, amountKES } = data;

  switch (paymentMethod) {
    case 'MPESA':
      return mpesaService.initiatePayment({
        userAddress,
        packageId,
        amountKES,
        phoneNumber: data.phoneNumber
      });

    case 'BANK':
    case 'CARD':
      return pesapalService.initiatePayment({
        userAddress,
        packageId,
        amountKES,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phoneNumber
      });

    case 'CRYPTO':
      return cryptoService.initiatePayment({
        userAddress,
        packageId,
        amountKES
      });

    default:
      throw new Error(`Unsupported payment method: ${paymentMethod}`);
  }
}

/**
 * Confirms a payment manually
 */
export async function confirmPayment(data: z.infer<typeof confirmPaymentSchema>) {
  return confirmPaymentCommon(data.paymentId, data.transactionRef);
}

/**
 * Gets the status of a payment
 */
export async function getStatus(paymentId: string) {
  return getPaymentStatus(paymentId);
}

/**
 * Initialize Pesapal IPN registration
 * This should be called when the application starts
 */
export async function initializePesapal() {
  try {
    await pesapalService.registerIPN();
    console.log('Pesapal IPN URL registered successfully');
  } catch (error) {
    console.error('Failed to register Pesapal IPN URL:', error);
  }
}

// Re-export service functions for direct access
export {
  mpesaService,
  pesapalService,
  cryptoService
};
