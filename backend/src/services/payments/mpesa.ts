// backend/src/services/payments/mpesa.ts
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { savePayment, updatePaymentStatus, getPayment } from '../../utils/db';
import { confirmPayment } from './common';
import { Request, Response } from 'express';

const {
  MPESA_CONSUMER_KEY,
  MPESA_CONSUMER_SECRET,
  MPESA_PASSKEY,
  BACKEND_URL
} = process.env;

// Default sandbox shortcode if not provided
const MPESA_SHORTCODE = process.env.MPESA_SHORTCODE || '174379';
console.log('Using M-Pesa shortcode:', MPESA_SHORTCODE);

interface InitiatePaymentArgs {
  userAddress: string;
  packageId: string;
  amountKES: number;
  phoneNumber?: string; // Optional, could be fetched from user profile
}

interface InitiateResult {
  paymentId: string;
  instructions: {
    checkoutRequestID: string;
    phoneNumber?: string;
  };
}

/**
 * Initiates an M-Pesa payment using STK Push
 */
export async function initiatePayment({
  userAddress,
  packageId,
  amountKES,
  phoneNumber = '************' // Default for testing, in production get from user profile
}: InitiatePaymentArgs): Promise<InitiateResult> {
  const paymentId = uuidv4();

  // Save initial DB record
  await savePayment({
    paymentId,
    userAddress,
    packageId,
    method: 'MPESA',
    amountKES,
    status: 'pending'
  });

  // 1. Get OAuth token with retry logic
  let access_token;
  let retries = 0;
  const maxRetries = 3;

  while (retries < maxRetries) {
    try {
      console.log(`Attempting to get M-Pesa OAuth token (attempt ${retries + 1}/${maxRetries})...`);

      const response = await axios.get(
        `https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials`,
        {
          auth: {
            username: MPESA_CONSUMER_KEY!,
            password: MPESA_CONSUMER_SECRET!
          },
          timeout: 10000, // 10 second timeout
          headers: {
            'Accept-Encoding': 'gzip, deflate, br'
          }
        }
      );

      access_token = response.data.access_token;
      console.log('Successfully obtained M-Pesa OAuth token');
      break;
    } catch (error: any) {
      retries++;
      const errorMessage = error.message || 'Unknown error';
      console.error(`Error getting M-Pesa OAuth token (attempt ${retries}/${maxRetries}):`, errorMessage);

      if (retries >= maxRetries) {
        throw new Error(`Failed to get M-Pesa OAuth token after ${maxRetries} attempts: ${errorMessage}`);
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * retries));
    }
  }

  // 2. STK Push with retry logic
  let pushRes;
  retries = 0;

  while (retries < maxRetries) {
    try {
      console.log(`Attempting to send M-Pesa STK Push (attempt ${retries + 1}/${maxRetries})...`);

      // Generate a fresh timestamp and password for each attempt
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
      const password = Buffer.from(`${MPESA_SHORTCODE}${MPESA_PASSKEY}${timestamp}`).toString('base64');

      // Format phone number correctly (ensure it starts with 254)
      let formattedPhone = phoneNumber;
      if (formattedPhone.startsWith('0')) {
        formattedPhone = `254${formattedPhone.substring(1)}`;
      } else if (!formattedPhone.startsWith('254')) {
        formattedPhone = `254${formattedPhone}`;
      }

      // Ensure amount is an integer
      const formattedAmount = Math.round(amountKES);

      // Prepare the request payload
      const payload = {
        BusinessShortCode: MPESA_SHORTCODE,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: formattedAmount,
        PartyA: formattedPhone,
        PartyB: MPESA_SHORTCODE,
        PhoneNumber: formattedPhone,
        CallBackURL: `${BACKEND_URL}/api/payments/mpesa/callback`,
        AccountReference: paymentId,
        TransactionDesc: `BlockCoop purchase ${packageId}`
      };

      console.log('STK Push payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(
        'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest',
        payload,
        {
          headers: {
            Authorization: `Bearer ${access_token}`,
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json'
          },
          timeout: 15000 // 15 second timeout
        }
      );

      pushRes = response.data;
      console.log('Successfully sent M-Pesa STK Push');
      break;
    } catch (error: any) {
      retries++;
      const errorMessage = error.message || 'Unknown error';
      console.error(`Error sending M-Pesa STK Push (attempt ${retries}/${maxRetries}):`, errorMessage);

      // Log detailed error information if available
      if (error.response) {
        console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        console.error('Error request:', error.request);
      }

      if (retries >= maxRetries) {
        const detailedError = error.response?.data
          ? `${errorMessage} - ${JSON.stringify(error.response.data)}`
          : errorMessage;
        throw new Error(`Failed to send M-Pesa STK Push after ${maxRetries} attempts: ${detailedError}`);
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * retries));
    }
  }

  // Store the checkout request ID in the payment record
  const instructions = {
    checkoutRequestID: pushRes.CheckoutRequestID,
    phoneNumber
  };

  await updatePaymentStatus(paymentId, 'pending');

  // Update the payment with the checkout request ID
  const payment = await getPayment(paymentId);
  if (payment) {
    payment.instructions = instructions;
  }

  return {
    paymentId,
    instructions
  };
}

/**
 * Handles the M-Pesa callback from Safaricom
 */
export async function handleMpesaCallback(req: Request, res: Response) {
  try {
    const { Body: { stkCallback } } = req.body;
    const { ResultCode, ResultDesc } = stkCallback;

    // Extract payment ID from the callback metadata
    let paymentId = '';
    let transactionId = '';

    if (stkCallback.CallbackMetadata && stkCallback.CallbackMetadata.Item) {
      const items = stkCallback.CallbackMetadata.Item;

      // Find the AccountReference (our paymentId) and TransactionID
      for (const item of items) {
        if (item.Name === 'AccountReference') {
          paymentId = item.Value;
        }
        if (item.Name === 'MpesaReceiptNumber') {
          transactionId = item.Value;
        }
      }
    }

    if (!paymentId) {
      console.error('Payment ID not found in M-Pesa callback');
      return res.status(400).json({ error: 'Payment ID not found' });
    }

    if (ResultCode === 0) {
      // Payment successful
      console.log(`M-Pesa payment successful: ${paymentId}, TransactionID: ${transactionId}`);
      await processSuccessfulPayment(paymentId, transactionId);
    } else {
      // Payment failed
      console.error(`M-Pesa payment failed: ${paymentId}, ResultCode: ${ResultCode}, ResultDesc: ${ResultDesc}`);
      await updatePaymentStatus(paymentId, 'failed');
    }

    // Always respond with 200 OK to acknowledge receipt
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error handling M-Pesa callback:', error);
    // Still respond with 200 to prevent Safaricom from retrying
    res.status(200).json({ success: false, error: 'Internal server error' });
  }
}

/**
 * Checks the status of an M-Pesa payment
 * This can be used if the callback hasn't been received
 */
export async function checkMpesaStatus(checkoutRequestID: string) {
  try {
    // Get OAuth token with retry logic
    let access_token;
    let retries = 0;
    const maxRetries = 3;

    // 1. Get OAuth token
    while (retries < maxRetries) {
      try {
        console.log(`Attempting to get M-Pesa OAuth token for status check (attempt ${retries + 1}/${maxRetries})...`);

        const response = await axios.get(
          `https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials`,
          {
            auth: {
              username: MPESA_CONSUMER_KEY!,
              password: MPESA_CONSUMER_SECRET!
            },
            timeout: 10000, // 10 second timeout
            headers: {
              'Accept-Encoding': 'gzip, deflate, br'
            }
          }
        );

        access_token = response.data.access_token;
        console.log('Successfully obtained M-Pesa OAuth token for status check');
        break;
      } catch (error: any) {
        retries++;
        const errorMessage = error.message || 'Unknown error';
        console.error(`Error getting M-Pesa OAuth token for status check (attempt ${retries}/${maxRetries}):`, errorMessage);

        if (retries >= maxRetries) {
          throw new Error(`Failed to get M-Pesa OAuth token for status check after ${maxRetries} attempts: ${errorMessage}`);
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }

    // 2. Query transaction status with retry logic
    retries = 0;
    let statusData;

    while (retries < maxRetries) {
      try {
        console.log(`Attempting to check M-Pesa status (attempt ${retries + 1}/${maxRetries})...`);

        // Generate a fresh timestamp and password for each attempt
        const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
        const password = Buffer.from(`${MPESA_SHORTCODE}${MPESA_PASSKEY}${timestamp}`).toString('base64');

        const response = await axios.post(
          'https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query',
          {
            BusinessShortCode: MPESA_SHORTCODE,
            Password: password,
            Timestamp: timestamp,
            CheckoutRequestID: checkoutRequestID
          },
          {
            headers: {
              Authorization: `Bearer ${access_token}`,
              'Accept-Encoding': 'gzip, deflate, br'
            },
            timeout: 15000 // 15 second timeout
          }
        );

        statusData = response.data;
        console.log('Successfully checked M-Pesa status');
        break;
      } catch (error: any) {
        retries++;
        const errorMessage = error.message || 'Unknown error';
        console.error(`Error checking M-Pesa status (attempt ${retries}/${maxRetries}):`, errorMessage);

        if (retries >= maxRetries) {
          throw new Error(`Failed to check M-Pesa status after ${maxRetries} attempts: ${errorMessage}`);
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }

    return statusData;
  } catch (error: any) {
    console.error('Error checking M-Pesa status:', error.message);
    throw error;
  }
}

/**
 * Process a successful M-Pesa payment
 * This is used by both the callback handler and the status checker
 */
export async function processSuccessfulPayment(paymentId: string, transactionRef: string) {
  try {
    // Update payment status
    await updatePaymentStatus(paymentId, 'confirmed', transactionRef);

    // Trigger the confirmation process (minting tokens, etc.)
    await confirmPayment(paymentId, transactionRef);

    return {
      success: true,
      paymentId,
      transactionRef
    };
  } catch (error) {
    console.error(`Error processing successful M-Pesa payment ${paymentId}:`, error);
    throw error;
  }
}
