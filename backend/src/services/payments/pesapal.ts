// backend/src/services/payments/pesapal.ts
import axios from 'axios';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import jwt from 'jsonwebtoken';
import { savePayment, updatePaymentStatus } from '../../utils/db';
import { confirmPayment } from './common';
import { Request, Response } from 'express';

// Environment variables (add to .env and .env.example)
const {
  PESAPAL_CONSUMER_KEY,
  PESAPAL_CONSUMER_SECRET,
  PESAPAL_IPN_URL,
  BACKEND_URL
} = process.env;

// Pesapal base URL (sandbox)
const BASE_URL = 'https://cybqa.pesapal.com';

/**
 * Register IPN URL with Pesapal
 * This should be called at application startup
 */
export async function registerIPN() {
  try {
    const credentials = `${PESAPAL_CONSUMER_KEY}:${PESAPAL_CONSUMER_SECRET}`;
    const authHeader = `Basic ${Buffer.from(credentials).toString('base64')}`;
    const url = `${BASE_URL}/API/RegisterIPNURL`;
    const payload = {
      url: PESAPAL_IPN_URL,
      ipn_notification_type: 'POST'
    };

    const sig = crypto
      .createHmac('sha1', PESAPAL_CONSUMER_SECRET!)
      .update(JSON.stringify(payload))
      .digest('base64');

    const response = await axios.post(url, payload, {
      headers: {
        Authorization: authHeader,
        'Content-Type': 'application/json',
        'Pesapal-Notification-HMAC-SHA1': sig
      }
    });

    console.log('Pesapal IPN URL registered successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to register Pesapal IPN URL:', error);
    throw error;
  }
}

interface InitiatePaymentArgs {
  userAddress: string;
  packageId: string;
  amountKES: number;
  email?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
}

/**
 * Initiates a payment using Pesapal
 */
export async function initiatePayment({
  userAddress,
  packageId,
  amountKES,
  email = '<EMAIL>',
  firstName = 'Customer',
  lastName = 'User',
  phoneNumber
}: InitiatePaymentArgs) {
  try {
    const paymentId = uuidv4();

    // Save initial payment record
    await savePayment({
      paymentId,
      userAddress,
      packageId,
      method: 'PESAPAL',
      amountKES,
      status: 'pending'
    });

    // Prepare authentication
    const credentials = `${PESAPAL_CONSUMER_KEY}:${PESAPAL_CONSUMER_SECRET}`;
    const authHeader = `Basic ${Buffer.from(credentials).toString('base64')}`;
    const url = `${BASE_URL}/API/PostPesapalDirectOrderV4`;

    // Prepare transaction data
    const transaction = {
      amount: amountKES,
      currency: 'KES',
      description: `BlockCoop purchase ${packageId}`,
      type: 'MERCHANT',
      reference: paymentId,
      first_name: firstName,
      last_name: lastName,
      email: email,
      phone_number: phoneNumber,
      ipn_url: PESAPAL_IPN_URL
    };

    // Sign the transaction with JWT
    const token = jwt.sign(transaction, PESAPAL_CONSUMER_SECRET!, { algorithm: 'HS256' });

    // Send request to Pesapal
    const response = await axios.post(url, { ...transaction, token }, {
      headers: {
        Authorization: authHeader,
        'Content-Type': 'application/json'
      }
    });

    // Return payment details with iframe URL
    return {
      paymentId,
      paymentLink: response.data.iframe_url,
      instructions: {
        message: 'Click the payment link to complete your payment',
        expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
      }
    };
  } catch (error) {
    console.error('Error initiating Pesapal payment:', error);
    throw error;
  }
}

/**
 * Handles the IPN (Instant Payment Notification) from Pesapal
 */
export async function handlePesapalIPN(req: Request, res: Response) {
  try {
    const ipn = req.body;
    console.log('Received Pesapal IPN:', ipn);

    // Validate HMAC if provided
    // In production, you should verify the signature

    const { OrderTrackingId, PaymentStatus, OrderMerchantReference } = ipn;
    const paymentId = OrderMerchantReference; // This is our paymentId

    if (PaymentStatus === 'COMPLETED') {
      await processSuccessfulPayment(paymentId, OrderTrackingId);
      console.log(`Pesapal payment confirmed: ${paymentId}, OrderTrackingId: ${OrderTrackingId}`);
    } else {
      await updatePaymentStatus(paymentId, 'failed');
      console.error(`Pesapal payment failed: ${paymentId}, Status: ${PaymentStatus}`);
    }

    // Always respond with 200 OK to acknowledge receipt
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error handling Pesapal IPN:', error);
    // Still respond with 200 to prevent Pesapal from retrying
    res.status(200).json({ success: false, error: 'Internal server error' });
  }
}

/**
 * Handles the redirect from Pesapal after payment
 */
export function handlePesapalRedirect(req: Request, res: Response) {
  try {
    const { OrderMerchantReference, OrderTrackingId } = req.query;

    // Log the redirect
    console.log(`Pesapal redirect: MerchantRef=${OrderMerchantReference}, TrackingID=${OrderTrackingId}`);

    // In production, you would verify the payment status with Pesapal
    // before redirecting the user

    // Redirect to appropriate page
    if (BACKEND_URL) {
      res.redirect(`${BACKEND_URL}/payment-status?paymentId=${OrderMerchantReference}`);
    } else {
      res.redirect(`/payment-status?paymentId=${OrderMerchantReference}`);
    }
  } catch (error) {
    console.error('Error handling Pesapal redirect:', error);
    res.redirect('/payment-error');
  }
}

/**
 * Checks the status of a Pesapal transaction
 */
export async function checkTransactionStatus(orderTrackingId: string) {
  try {
    const credentials = `${PESAPAL_CONSUMER_KEY}:${PESAPAL_CONSUMER_SECRET}`;
    const authHeader = `Basic ${Buffer.from(credentials).toString('base64')}`;
    const url = `${BASE_URL}/API/QueryPaymentStatus`;

    const response = await axios.get(url, {
      params: { OrderTrackingId: orderTrackingId },
      headers: { Authorization: authHeader }
    });

    return response.data;
  } catch (error) {
    console.error('Error checking Pesapal transaction status:', error);
    throw error;
  }
}

/**
 * Process a successful Pesapal payment
 * This is used by both the IPN handler and the status checker
 */
export async function processSuccessfulPayment(paymentId: string, orderTrackingId: string) {
  try {
    // Update payment status
    await updatePaymentStatus(paymentId, 'confirmed', orderTrackingId);

    // Trigger the confirmation process (minting tokens, etc.)
    await confirmPayment(paymentId, orderTrackingId);

    return {
      success: true,
      paymentId,
      orderTrackingId
    };
  } catch (error) {
    console.error(`Error processing successful Pesapal payment ${paymentId}:`, error);
    throw error;
  }
}
