// backend/src/services/payments/aggregator.ts
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';
import { savePayment, updatePaymentStatus } from '../../utils/db';
import { confirmPayment } from './common';
import crypto from 'crypto';
import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';

// Environment variables
const {
  BACKEND_URL = 'http://localhost:3000',
  PESAPAL_BASE_URL = 'https://pay.pesapal.com/v3',
  PESAPAL_CONSUMER_KEY = 'PESAPAL_CONSUMER_KEY',
  PESAPAL_CONSUMER_SECRET = 'PESAPAL_CONSUMER_SECRET',
  PESAPAL_IPN_ID = 'PESAPAL_IPN_ID'
} = process.env;

// Zod schema for initiate
const initiateSchema = z.object({
  userAddress: z.string().startsWith('0x'),
  packageId: z.string(),
  amountKES: z.number().positive(),
  email: z.string().email().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phoneNumber: z.string().optional()
});

/**
 * Initiates a payment using Pesapal (for BANK and CARD payments)
 */
export async function initiatePayment({
  userAddress,
  packageId,
  amountKES,
  email = '<EMAIL>', // Default for testing
  firstName = 'Test',             // Default for testing
  lastName = 'User',              // Default for testing
  phoneNumber = '************'    // Default for testing
}: z.infer<typeof initiateSchema>) {
  const paymentId = uuidv4();

  // Save initial record
  await savePayment({
    paymentId,
    userAddress,
    packageId,
    method: 'PESAPAL',
    amountKES,
    status: 'pending'
  });

  // Get auth token
  const token = await getAuthToken();

  // Build the request payload
  const payload = {
    id: paymentId,
    currency: 'KES',
    amount: amountKES,
    description: `BlockCoop Share Purchase - Package ${packageId}`,
    callback_url: `${BACKEND_URL}/api/payments/pesapal/redirect`,
    notification_id: PESAPAL_IPN_ID,
    billing_address: {
      email_address: email,
      phone_number: phoneNumber,
      first_name: firstName,
      last_name: lastName,
      country_code: 'KE'
    }
  };

  // Call Pesapal API
  const { data } = await axios.post(
    `${PESAPAL_BASE_URL}/orders`,
    payload,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  // Return the payment link and ID
  return {
    paymentId,
    paymentLink: data.redirect_url,
    instructions: {
      message: 'Click the payment link to complete your payment',
      expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
    }
  };
}

/**
 * Gets an authentication token from Pesapal
 */
async function getAuthToken() {
  try {
    // Create token payload
    const payload = {
      consumer_key: PESAPAL_CONSUMER_KEY,
      consumer_secret: PESAPAL_CONSUMER_SECRET
    };

    // Get token from Pesapal
    const { data } = await axios.post(
      `${PESAPAL_BASE_URL}/auth/token`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    return data.token;
  } catch (error) {
    console.error('Error getting Pesapal auth token:', error);
    throw error;
  }
}

/**
 * Handles the IPN (Instant Payment Notification) from Pesapal
 */
export async function handlePesapalIPN(req: Request, res: Response) {
  try {
    // Extract data from request
    const {
      OrderTrackingId,
      OrderMerchantReference,
      OrderNotificationType,
      PaymentStatus
    } = req.body;

    // Validate required fields
    if (!OrderMerchantReference || !PaymentStatus) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Get payment ID from merchant reference
    const paymentId = OrderMerchantReference;

    // Process based on payment status
    if (PaymentStatus === 'COMPLETED') {
      // Update payment status
      await updatePaymentStatus(paymentId, 'confirmed', OrderTrackingId);
      await confirmPayment(paymentId, OrderTrackingId);

      console.log(`Pesapal payment confirmed: ${paymentId}, TrackingID: ${OrderTrackingId}`);
    } else if (PaymentStatus === 'FAILED') {
      await updatePaymentStatus(paymentId, 'failed');
      console.error(`Pesapal payment failed: ${paymentId}, Status: ${PaymentStatus}`);
    }

    // Acknowledge receipt
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error handling Pesapal IPN:', error);
    // Still respond with 200 to prevent Pesapal from retrying
    res.status(200).json({ success: false, error: 'Internal server error' });
  }
}

/**
 * Handles the redirect from Pesapal after payment
 */
export function handlePesapalRedirect(req: Request, res: Response) {
  try {
    const { OrderMerchantReference, OrderTrackingId } = req.query;

    // Log the redirect
    console.log(`Pesapal redirect: MerchantRef=${OrderMerchantReference}, TrackingID=${OrderTrackingId}`);

    // Redirect to appropriate page
    res.redirect(`/payment-status?paymentId=${OrderMerchantReference}`);
  } catch (error) {
    console.error('Error handling Pesapal redirect:', error);
    res.redirect('/payment-error');
  }
}

/**
 * Checks the status of a Pesapal transaction
 */
export async function checkPesapalStatus(orderTrackingId: string) {
  try {
    // Get auth token
    const token = await getAuthToken();

    // Call Pesapal API to check status
    const { data } = await axios.get(
      `${PESAPAL_BASE_URL}/transactions/${orderTrackingId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );

    return data;
  } catch (error) {
    console.error('Error checking Pesapal status:', error);
    throw error;
  }
}
