// backend/src/services/payments/crypto.ts
import { v4 as uuidv4 } from 'uuid';
import { savePayment, updatePaymentStatus } from '../../utils/db';
import { confirmPayment } from './common';
import { publicClient, getWalletAddress } from '../web3';
import { parseUnits } from 'viem';

// Environment variables
const {
  USDT_CONTRACT_ADDRESS,
  DEPOSIT_ADDRESS
} = process.env;

// USDT contract ABI (minimal for transfer events)
const USDT_ABI = [
  {
    anonymous: false,
    inputs: [
      { indexed: true, name: 'from', type: 'address' },
      { indexed: true, name: 'to', type: 'address' },
      { indexed: false, name: 'value', type: 'uint256' }
    ],
    name: 'Transfer',
    type: 'event'
  }
] as const;

// Module-level map to store active listeners
const cryptoListeners = new Map<string, NodeJS.Timeout>();

interface InitiatePaymentArgs {
  userAddress: string;
  packageId: string;
  amountKES: number;
}

/**
 * Initiates a crypto payment by generating a deposit address and instructions
 */
export async function initiatePayment({
  userAddress,
  packageId,
  amountKES
}: InitiatePaymentArgs) {
  const paymentId = uuidv4();

  // Calculate USDT amount based on KES (using a simple conversion rate for demo)
  // In production, you would use a real exchange rate API
  const exchangeRate = 0.0075; // 1 KES = 0.0075 USDT (example rate)
  const amountUSDT = amountKES * exchangeRate;

  // Format amount with 6 decimals (USDT standard)
  const formattedAmount = amountUSDT.toFixed(6);

  // Save initial payment record
  await savePayment({
    paymentId,
    userAddress,
    packageId,
    method: 'CRYPTO',
    amountKES,
    status: 'pending'
  });

  // In production, you might generate a unique deposit address per payment
  // For simplicity, we'll use a fixed address from environment variables
  const depositAddress = DEPOSIT_ADDRESS || getWalletAddress();

  // Start listening for the transfer event
  startTransferListener(paymentId, depositAddress, parseUnits(formattedAmount, 6));

  return {
    paymentId,
    instructions: {
      depositAddress,
      amountUSDT: formattedAmount,
      currency: 'USDT',
      network: 'BSC',
      minConfirmations: 1
    }
  };
}

/**
 * Starts listening for USDT transfer events to the deposit address
 */
function startTransferListener(paymentId: string, depositAddress: string, requiredAmount: bigint) {
  if (!USDT_CONTRACT_ADDRESS) {
    console.error('USDT_CONTRACT_ADDRESS not configured');
    return;
  }

  console.log(`Starting transfer listener for payment ${paymentId}`);
  console.log(`Waiting for ${requiredAmount} USDT to ${depositAddress}`);

  // Check if we already have a listener for this payment
  if (cryptoListeners.has(paymentId)) {
    console.log(`Listener for payment ${paymentId} already exists, skipping`);
    return;
  }

  // In production, this would be a real blockchain event listener
  // For demo purposes, we'll use a timeout to simulate a received transfer

  // Create a unique identifier for this listener
  const listenerId = setTimeout(async () => {
    try {
      console.log(`Simulating received transfer for payment ${paymentId}`);

      // Generate a fake transaction hash
      const txHash = `0x${Array.from({length: 64}, () =>
        Math.floor(Math.random() * 16).toString(16)).join('')}`;

      // Confirm the payment
      await processSuccessfulPayment(paymentId, txHash);

      // Clean up the listener
      cleanupListener(paymentId);
    } catch (error) {
      console.error(`Error processing crypto payment ${paymentId}:`, error);
    }
  }, 10000);

  // Store the listener reference for cleanup
  cryptoListeners.set(paymentId, listenerId);
}

/**
 * Cleans up a crypto payment listener
 */
function cleanupListener(paymentId: string) {
  if (cryptoListeners.has(paymentId)) {
    const listenerId = cryptoListeners.get(paymentId);
    if (listenerId) {
      clearTimeout(listenerId);
    }
    cryptoListeners.delete(paymentId);
    console.log(`Cleaned up listener for payment ${paymentId}`);
  }
}

/**
 * Process a successful crypto payment
 */
export async function processSuccessfulPayment(paymentId: string, txHash: string) {
  try {
    // Update payment status
    await updatePaymentStatus(paymentId, 'confirmed', txHash);

    // Trigger the confirmation process (minting tokens, etc.)
    await confirmPayment(paymentId, txHash);

    return {
      success: true,
      paymentId,
      txHash
    };
  } catch (error) {
    console.error(`Error processing successful crypto payment ${paymentId}:`, error);
    throw error;
  }
}

/**
 * Manually checks for a USDT transfer
 * This could be used for manual verification or as a fallback
 */
export async function checkTransfer(paymentId: string, txHash: string) {
  try {
    // In production, you would verify the transaction on-chain
    // For now, we'll just process the payment
    await processSuccessfulPayment(paymentId, txHash);

    // Clean up any existing listener for this payment
    cleanupListener(paymentId);

    return {
      success: true,
      paymentId,
      txHash,
      message: 'Transfer verified and payment confirmed'
    };
  } catch (error) {
    console.error(`Error checking transfer for payment ${paymentId}:`, error);

    return {
      success: false,
      paymentId,
      txHash,
      message: 'Failed to verify transfer'
    };
  }
}
