// backend/src/services/web3.ts

import { createPublicClient, createWalletClient, http } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { bscTestnet, bsc } from 'viem/chains';

// load environment variables
const {
  BSC_RPC = 'https://bsc-testnet.drpc.org',
  BSC_CHAIN_ID = '97',
  PRIVATE_KEY,                  // your multisig relayer or deployer key
  NODE_ENV = 'development',
} = process.env;

// choose chain based on environment
const chain = NODE_ENV === 'production' ? bsc : bscTestnet;
const rpcUrl = BSC_RPC;
const chainId = Number(BSC_CHAIN_ID);

// public client for read-only calls
export const publicClient = createPublicClient({
  chain,
  transport: http(rpcUrl),
});

// wallet client for signed transactions
export const walletClient = createWalletClient({
  chain,
  transport: http(rpcUrl),
  account: PRIVATE_KEY
    ? privateKeyToAccount(`0x${PRIVATE_KEY}`)
    : undefined,
});

/**
 * Example helper: get current block number
 */
export async function getBlockNumber() {
  return publicClient.getBlockNumber();
}

/**
 * Example helper: send a transaction
 */
export async function sendTx(params: {
  to: `0x${string}`;
  value?: bigint;
  data?: `0x${string}`;
}) {
  if (!walletClient.account) {
    throw new Error('No PRIVATE_KEY configured for walletClient');
  }
  return walletClient.sendTransaction({
    to: params.to,
    value: params.value,
    data: params.data,
    chain,
    account: walletClient.account,
  });
}

/**
 * Get the current nonce for the wallet account
 */
export async function getAccountNonce() {
  if (!walletClient.account) {
    throw new Error('No PRIVATE_KEY configured for walletClient');
  }
  const nonce = await publicClient.getTransactionCount({
    address: walletClient.account.address,
  });
  return BigInt(nonce);
}

/**
 * Get the wallet account address
 */
export function getWalletAddress() {
  if (!walletClient.account) {
    throw new Error('No PRIVATE_KEY configured for walletClient');
  }
  return walletClient.account.address;
}
