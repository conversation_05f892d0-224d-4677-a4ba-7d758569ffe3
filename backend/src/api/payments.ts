// backend/src/api/payments.ts
import { Router, Request, Response, NextFunction } from 'express';
import {
  initiatePayment,
  confirmPayment,
  getStatus,
  initiatePaymentSchema,
  confirmPaymentSchema,
  mpesaService,
  pesapalService,
  cryptoService
} from '../services/payments';
import { ZodError } from 'zod';
import { rateLimit } from 'express-rate-limit';
import { safaricomProxy } from '../middleware/safaricomProxy';

const router = Router();

// Rate limiting middleware
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again after 15 minutes'
});

// Initiate payment rate limiter (more strict)
const initiatePaymentLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // limit each IP to 10 payment initiations per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many payment requests from this IP, please try again after 5 minutes'
});

// Authentication middleware (placeholder)
// In production, will replace with proper JWT or API key validation
const authenticate = (_req: Request, _res: Response, next: NextFunction) => {
  // For development, just pass through
  // In production, will validate JWT or API key
  next();
};

// Error handling middleware
const errorHandler = (err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error('API Error:', err);

  if (err instanceof ZodError) {
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      details: err.errors
    });
  }

  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
};

// Apply rate limiting to all routes
router.use(apiLimiter);

// Initiate payment route
router.post('/initiate', authenticate, initiatePaymentLimiter, async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate the request body
    try {
      const data = initiatePaymentSchema.parse(req.body);
      const result = await initiatePayment(data);
      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.errors
        });
      }
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

// Confirm payment route (manual confirmation)
router.post('/confirm', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate the request body
    try {
      const data = confirmPaymentSchema.parse(req.body);
      const result = await confirmPayment(data);
      res.json(result);
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.errors
        });
      }
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

// Get payment status route
router.get('/status/:paymentId', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { paymentId } = req.params;
    const result = await getStatus(paymentId);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// M-Pesa callback route
router.post('/mpesa/callback', async (req: Request, res: Response, next: NextFunction) => {
  try {
    await mpesaService.handleMpesaCallback(req, res);
  } catch (error) {
    next(error);
  }
});

// Pesapal IPN route
router.post('/pesapal/ipn', async (req: Request, res: Response, next: NextFunction) => {
  try {
    await pesapalService.handlePesapalIPN(req, res);
  } catch (error) {
    next(error);
  }
});

// Pesapal redirect route
router.get('/pesapal/redirect', async (req: Request, res: Response, next: NextFunction) => {
  try {
    pesapalService.handlePesapalRedirect(req, res);
  } catch (error) {
    next(error);
  }
});

// Crypto transaction check route
router.post('/crypto/check', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { paymentId, txHash } = req.body;

    if (!paymentId || !txHash) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        message: 'paymentId and txHash are required'
      });
    }

    const result = await cryptoService.checkTransfer(paymentId, txHash);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Safaricom proxy route (for testing and debugging)
router.post('/safaricom-proxy/:endpoint(*)', async (req: Request, res: Response, next: NextFunction) => {
  try {
    await safaricomProxy(req, res);
  } catch (error) {
    next(error);
  }
});

// Apply error handler
router.use(errorHandler);

export default router;
