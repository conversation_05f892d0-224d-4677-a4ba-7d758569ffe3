// backend/src/api/admin.ts
import { Router, Request, Response, NextFunction } from 'express';
import { runPaymentJobs } from '../jobs/runJobs';

const router = Router();

// Simple admin authentication middleware
// In production, replace with proper admin authentication
const adminAuth = (req: Request, res: Response, next: NextFunction) => {
  // For development, just pass through
  // In production, validate admin credentials
  next();
};

// Run jobs route
router.post('/run-jobs', adminAuth, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { jobType } = req.body;
    
    if (jobType === 'payments') {
      // Run payment jobs
      await runPaymentJobs();
      
      res.json({
        success: true,
        message: 'Payment jobs completed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Invalid job type',
        message: 'Supported job types: payments'
      });
    }
  } catch (error) {
    console.error('Error running jobs:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
