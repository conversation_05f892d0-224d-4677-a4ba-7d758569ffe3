// backend/src/middleware/safaricomProxy.ts
import axios from 'axios';
import { Request, Response } from 'express';

// Environment variables
const {
  MPESA_CONSUMER_KEY,
  MPESA_CONSUMER_SECRET
} = process.env;

/**
 * Proxy middleware for Safaricom API calls
 * This can help bypass CORS issues and network connectivity problems
 */
export async function safaricomProxy(req: Request, res: Response) {
  try {
    const { endpoint } = req.params;
    const { method, data, auth } = req.body;

    console.log(`Proxying Safaricom API call to ${endpoint}`);

    // Set default headers
    const headers: Record<string, string> = {
      'Accept-Encoding': 'gzip, deflate, br',
      'Content-Type': 'application/json'
    };

    // Add authorization if needed
    if (auth === 'oauth') {
      // Get OAuth token
      try {
        const authResponse = await axios.get(
          `https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials`,
          {
            auth: {
              username: MPESA_CONSUMER_KEY!,
              password: MPESA_CONSUMER_SECRET!
            },
            timeout: 10000
          }
        );

        headers['Authorization'] = `Bearer ${authResponse.data.access_token}`;
      } catch (error: any) {
        console.error('Error getting OAuth token for proxy:', error.message);
        return res.status(500).json({
          success: false,
          error: 'Failed to get OAuth token',
          message: error.message
        });
      }
    }

    // Log the request details
    console.log(`Making API call to ${endpoint}:`);
    console.log('Method:', method || 'GET');
    console.log('Headers:', JSON.stringify(headers, null, 2));
    console.log('Data:', JSON.stringify(data || {}, null, 2));

    // Make the API call
    const response = await axios({
      method: method || 'GET',
      url: `https://sandbox.safaricom.co.ke/${endpoint}`,
      data: data || {},
      headers,
      timeout: 15000
    });

    // Return the response
    return res.json({
      success: true,
      data: response.data
    });
  } catch (error: any) {
    console.error('Error in Safaricom proxy:', error.message);
    console.error('Error details:', error.response?.data || 'No response data');

    return res.status(500).json({
      success: false,
      error: 'Proxy error',
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      statusText: error.response?.statusText
    });
  }
}
