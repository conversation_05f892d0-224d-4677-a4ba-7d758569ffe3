// backend/src/utils/db.ts
import { v4 as uuidv4 } from 'uuid';

// This is a mock implementation for development
// In production, replace with actual database calls

export interface Payment {
  paymentId: string;
  userAddress: string;
  packageId: string;
  method: 'MPESA' | 'PESAPAL' | 'CRYPTO';
  amountKES: number;
  status: 'pending' | 'confirmed' | 'failed' | 'processing' | 'expired';
  transactionRef?: string;
  instructions?: any; // Store method-specific instructions
  createdAt: Date;
  updatedAt: Date;
}

// In-memory store for development
const payments: Record<string, Payment> = {};

export async function savePayment(data: Omit<Payment, 'createdAt' | 'updatedAt'>) {
  const now = new Date();
  const payment: Payment = {
    ...data,
    createdAt: now,
    updatedAt: now
  };

  payments[data.paymentId] = payment;
  console.log(`Payment saved: ${data.paymentId}`);
  return payment;
}

export async function getPayment(paymentId: string): Promise<Payment | null> {
  return payments[paymentId] || null;
}

export async function updatePaymentStatus(
  paymentId: string,
  status: Payment['status'],
  transactionRef?: string
) {
  const payment = payments[paymentId];
  if (!payment) {
    throw new Error(`Payment not found: ${paymentId}`);
  }

  payment.status = status;
  payment.updatedAt = new Date();

  if (transactionRef) {
    payment.transactionRef = transactionRef;
  }

  console.log(`Payment ${paymentId} status updated to ${status}`);
  return payment;
}

export async function listPaymentsByUser(userAddress: string): Promise<Payment[]> {
  return Object.values(payments).filter(p => p.userAddress === userAddress);
}

/**
 * Get pending payments based on criteria
 */
export async function getPendingPayments(options: {
  method?: Payment['method'];
  olderThanMinutes?: number;
  newerThanMinutes?: number;
}): Promise<Payment[]> {
  const { method, olderThanMinutes, newerThanMinutes } = options;
  const now = new Date();

  return Object.values(payments).filter(payment => {
    // Filter by status
    if (payment.status !== 'pending') {
      return false;
    }

    // Filter by method if specified
    if (method && payment.method !== method) {
      return false;
    }

    // Filter by age (older than)
    if (olderThanMinutes !== undefined) {
      const minAge = new Date(now.getTime() - olderThanMinutes * 60 * 1000);
      if (payment.createdAt > minAge) {
        return false;
      }
    }

    // Filter by age (newer than)
    if (newerThanMinutes !== undefined) {
      const maxAge = new Date(now.getTime() - newerThanMinutes * 60 * 1000);
      if (payment.createdAt < maxAge) {
        return false;
      }
    }

    return true;
  });
}

// For testing purposes
export function clearPayments() {
  Object.keys(payments).forEach(key => {
    delete payments[key];
  });
}
