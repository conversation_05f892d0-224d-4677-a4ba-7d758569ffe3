// backend/src/index.ts
import express from 'express';
import paymentsRouter from './api/payments';
import adminRouter from './api/admin';
import { initializePesapal } from './services/payments';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Routes
app.use('/api/payments', paymentsRouter);
app.use('/api/admin', adminRouter);

// Start server
app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);

  // Initialize Pesapal IPN registration
  try {
    await initializePesapal();
    console.log('Pesapal initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Pesapal:', error);
  }
});
