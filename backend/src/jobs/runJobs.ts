// backend/src/jobs/runJobs.ts
import { checkPendingMpesaPayments, checkPendingPesapalPayments, cleanupStalePayments } from './checkPendingPayments';

/**
 * Run all payment-related jobs
 * This can be called from a cron job or scheduled task
 */
export async function runPaymentJobs() {
  console.log('Running payment jobs...');
  
  try {
    // Check pending M-Pesa payments
    await checkPendingMpesaPayments();
    
    // Check pending Pesapal payments
    await checkPendingPesapalPayments();
    
    // Clean up stale payments
    await cleanupStalePayments();
    
    console.log('Payment jobs completed successfully');
  } catch (error) {
    console.error('Error running payment jobs:', error);
  }
}

// If this script is run directly, execute the jobs
if (require.main === module) {
  runPaymentJobs()
    .then(() => {
      console.log('Jobs completed, exiting...');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error running jobs:', error);
      process.exit(1);
    });
}
