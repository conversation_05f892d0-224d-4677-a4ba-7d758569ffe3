// backend/src/jobs/checkPendingPayments.ts
import { getPendingPayments, updatePaymentStatus } from '../utils/db';
import { mpesaService, pesapalService } from '../services/payments';

/**
 * Checks the status of pending M-Pesa payments
 * This job should be scheduled to run periodically (e.g., every 5 minutes)
 * to catch any payments that didn't receive callbacks
 */
export async function checkPendingMpesaPayments() {
  try {
    console.log('Checking pending M-Pesa payments...');
    
    // Get all pending M-Pesa payments older than 2 minutes
    // (to give time for the callback to arrive naturally)
    const pendingPayments = await getPendingPayments({
      method: 'MPESA',
      olderThanMinutes: 2,
      newerThanMinutes: 60 // Don't check payments older than 60 minutes
    });

    console.log(`Found ${pendingPayments.length} pending M-Pesa payments to check`);

    // Process each pending payment
    for (const payment of pendingPayments) {
      try {
        // Extract checkoutRequestID from payment instructions
        const { instructions } = payment;
        if (!instructions || !instructions.checkoutRequestID) {
          console.error(`Payment ${payment.paymentId} has no checkoutRequestID`);
          continue;
        }

        const { checkoutRequestID } = instructions;
        console.log(`Checking status of M-Pesa payment ${payment.paymentId} with checkoutRequestID ${checkoutRequestID}`);

        // Check payment status
        const statusResponse = await mpesaService.checkMpesaStatus(checkoutRequestID);

        // Process the response
        if (statusResponse.ResultCode === 0) {
          // Payment successful
          console.log(`M-Pesa payment ${payment.paymentId} is successful`);
          await mpesaService.processSuccessfulPayment(payment.paymentId, checkoutRequestID);
        } else if ([1, 1032].includes(statusResponse.ResultCode)) {
          // 1: Transaction failed
          // 1032: Transaction cancelled by user
          console.log(`M-Pesa payment ${payment.paymentId} failed with code ${statusResponse.ResultCode}`);
          await updatePaymentStatus(payment.paymentId, 'failed');
        }
        // For other codes, keep as pending and check again later
      } catch (error) {
        console.error(`Error checking M-Pesa payment ${payment.paymentId}:`, error);
        // Don't update status on error, will retry next time
      }
    }

    console.log('Finished checking pending M-Pesa payments');
    return {
      success: true,
      checkedCount: pendingPayments.length
    };
  } catch (error) {
    console.error('Error in checkPendingMpesaPayments:', error);
    throw error;
  }
}

/**
 * Checks the status of pending Pesapal payments
 * This job should be scheduled to run periodically
 */
export async function checkPendingPesapalPayments() {
  try {
    console.log('Checking pending Pesapal payments...');
    
    // Get all pending Pesapal payments older than 5 minutes
    const pendingPayments = await getPendingPayments({
      method: 'PESAPAL',
      olderThanMinutes: 5,
      newerThanMinutes: 1440 // 24 hours
    });

    console.log(`Found ${pendingPayments.length} pending Pesapal payments to check`);

    // Process each pending payment
    for (const payment of pendingPayments) {
      try {
        // Extract orderTrackingId from payment
        const { transactionRef } = payment;
        if (!transactionRef) {
          console.error(`Payment ${payment.paymentId} has no transactionRef`);
          continue;
        }

        console.log(`Checking status of Pesapal payment ${payment.paymentId} with orderTrackingId ${transactionRef}`);

        // Check payment status
        const statusResponse = await pesapalService.checkTransactionStatus(transactionRef);

        // Process the response
        if (statusResponse.status === 'COMPLETED') {
          // Payment successful
          console.log(`Pesapal payment ${payment.paymentId} is successful`);
          await pesapalService.processSuccessfulPayment(payment.paymentId, transactionRef);
        } else if (['FAILED', 'CANCELLED'].includes(statusResponse.status)) {
          // Payment failed
          console.log(`Pesapal payment ${payment.paymentId} failed with status ${statusResponse.status}`);
          await updatePaymentStatus(payment.paymentId, 'failed');
        }
        // For other statuses, keep as pending and check again later
      } catch (error) {
        console.error(`Error checking Pesapal payment ${payment.paymentId}:`, error);
        // Don't update status on error, will retry next time
      }
    }

    console.log('Finished checking pending Pesapal payments');
    return {
      success: true,
      checkedCount: pendingPayments.length
    };
  } catch (error) {
    console.error('Error in checkPendingPesapalPayments:', error);
    throw error;
  }
}

/**
 * Cleans up stale pending payments
 * This job should be scheduled to run daily
 */
export async function cleanupStalePayments() {
  try {
    console.log('Cleaning up stale pending payments...');
    
    // Get all pending payments older than 24 hours
    const stalePayments = await getPendingPayments({
      olderThanMinutes: 1440, // 24 hours
    });

    console.log(`Found ${stalePayments.length} stale pending payments to clean up`);

    // Mark each stale payment as expired
    for (const payment of stalePayments) {
      try {
        console.log(`Marking payment ${payment.paymentId} as expired`);
        await updatePaymentStatus(payment.paymentId, 'expired');
      } catch (error) {
        console.error(`Error marking payment ${payment.paymentId} as expired:`, error);
      }
    }

    console.log('Finished cleaning up stale pending payments');
    return {
      success: true,
      cleanedCount: stalePayments.length
    };
  } catch (error) {
    console.error('Error in cleanupStalePayments:', error);
    throw error;
  }
}
