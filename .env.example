# BSC Testnet RPC Configuration
BSC_RPC=https://bsc-testnet.drpc.org
BSC_CHAIN_ID=97

# Wallet Configuration (DO NOT USE REAL PRIVATE KEYS IN PRODUCTION)
# This is only for development and testing purposes
# Format: hex string without 0x prefix
PRIVATE_KEY=your_private_key_here

# Node Environment
# Options: development, production
NODE_ENV=development

# Backend URL (used for callbacks)
BACKEND_URL=http://localhost:3000

# M-Pesa Configuration
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_PASSKEY=your_mpesa_passkey

# Pesapal Configuration
PESAPAL_CONSUMER_KEY=your_pesapal_consumer_key
PESAPAL_CONSUMER_SECRET=your_pesapal_consumer_secret
PESAPAL_IPN_URL=http://localhost:3000/api/payments/pesapal/ipn

# USDT Configuration
USDT_CONTRACT_ADDRESS=******************************************
DEPOSIT_ADDRESS=your_deposit_address

# Slack Webhook for CI/CD notifications (optional)
# SLACK_WEBHOOK_URL=your_slack_webhook_url_here
